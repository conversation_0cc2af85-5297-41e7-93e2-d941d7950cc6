import pandas as pd
import json

import seaborn as sns
import matplotlib.pyplot as plt
from langchain_openai import ChatOpenAI
from agent.prompts import PLOT_PROMPT
from agent.config import LLMs

def describe_dataframe_for_llm(df: pd.DataFrame) -> str:
    description = {
        "n_rows": df.shape[0],
        "n_columns": df.shape[1],
        "columns": {col: str(dtype) for col, dtype in df.dtypes.items()},
        "sample": df.head(5).to_dict(orient="records")
    }
    return json.dumps(description, indent=2)



def generate_plot_code(df: pd.DataFrame):
    df_description = describe_dataframe_for_llm(df)
    llm = LLMs.base_model
    messages = PLOT_PROMPT.format(dataframe_description=df_description)
    response = llm.invoke(messages)
    return response.content  # This will be a Python code block


def render_generated_plot(code_str: str, df: pd.DataFrame):
    local_vars = {"df": df, "plt": plt, "sns": sns, "pd": pd}
   
    exec(code_str, {}, local_vars)
    return local_vars

def auto_plot_from_dataframe(df: pd.DataFrame):
    generated_code = generate_plot_code(df)
    print("Generated Code:\n", generated_code)
    code_str = generated_code.lstrip("```python").rstrip("```")
    plot_vars = render_generated_plot(code_str, df)
    return plot_vars
