#!/usr/bin/env python3
"""
Script to run the Sherlock Agent Streamlit UI
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import streamlit
        import plotly
        import pandas
        print("✅ All required dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install dependencies with: pip install -r requirements.txt")
        return False

def main():
    """Main function to run the Streamlit app"""
    print("🔍 Sherlock Agent - Streamlit UI Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("streamlit_app.py").exists():
        print("❌ streamlit_app.py not found in current directory")
        print("Please run this script from the project root directory")
        return 1
    
    # Check dependencies
    if not check_dependencies():
        return 1
    
    # Set environment variables if needed
    os.environ.setdefault("PYTHONPATH", str(Path.cwd()))
    
    print("🚀 Starting Streamlit application...")
    print("📱 The UI will open in your default web browser")
    print("🔗 URL: http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Run streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Shutting down Streamlit server...")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
