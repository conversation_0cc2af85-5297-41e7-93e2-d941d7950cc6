from agent.config import openai_api_key
from agent.workflow import Agent
from langchain_core.messages import HumanMessage
from uuid import uuid4
conversation_id = str(uuid4().hex[:8])


def main(msg: str = "What is the total number of farmers?"):
    agent = Agent()
    flow = agent.build_workflow()
    config = {"configurable": {"thread_id": conversation_id}}
    messages = {"messages": [HumanMessage(content=msg)]}
    # response = agent.run(messages[0].content)
    # print(f"User:> {messages['messages'][0].content}")
    response = flow.invoke(messages, config=config)
    print(response['messages'][-1].content)

if __name__ == "__main__":
    while True:
        user_message = input("Enter Message:> ")
        main(user_message)
    # main()
