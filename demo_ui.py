#!/usr/bin/env python3
"""
Demo script for the Sherlock Agent Streamlit UI
This script demonstrates how to use the UI and provides sample interactions.
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def print_banner():
    """Print a welcome banner"""
    print("=" * 70)
    print("🔍 SHERLOCK AGENT - STREAMLIT UI DEMO")
    print("=" * 70)
    print("Welcome to the Sherlock Agent Streamlit User Interface!")
    print()
    print("This demo will:")
    print("1. 🚀 Launch the Streamlit application")
    print("2. 📱 Open it in your web browser")
    print("3. 📋 Show you how to use the interface")
    print("=" * 70)

def print_usage_guide():
    """Print usage instructions"""
    print("\n📋 HOW TO USE THE SHERLOCK AGENT UI")
    print("=" * 50)
    print()
    print("🔧 GETTING STARTED:")
    print("1. Wait for the agent to initialize (sidebar shows status)")
    print("2. Click '➕ New Session' in the sidebar")
    print("3. Type your question in the text area")
    print("4. Click '🚀 Send' to get your answer")
    print()
    print("💬 SAMPLE QUESTIONS TO TRY:")
    print("• 'What is the total number of farmers?'")
    print("• 'Show me the top 10 commodities by volume'")
    print("• 'What are the average loan amounts by region?'")
    print("• 'Display client segments and their counts'")
    print("• 'Show me recent transactions over $10,000'")
    print()
    print("🎯 FEATURES TO EXPLORE:")
    print("• 📊 Automatic data visualizations")
    print("• 📝 SQL query display with syntax highlighting")
    print("• 📈 Interactive data tables")
    print("• 🔄 Multiple session management")
    print("• 📋 Session statistics tracking")
    print()
    print("🔧 SIDEBAR FEATURES:")
    print("• Create and switch between sessions")
    print("• View session statistics (messages, queries, rows)")
    print("• Monitor agent initialization status")
    print("• Access system information and help")
    print()
    print("💡 TIPS:")
    print("• Use natural language - ask questions as you would to a colleague")
    print("• Be specific about what data you want to see")
    print("• Check the generated SQL to understand how queries work")
    print("• Use the visualizations to spot trends and patterns")
    print("• Create separate sessions for different analysis topics")
    print("=" * 50)

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("\n🔍 CHECKING PREREQUISITES...")
    
    # Check if streamlit_app.py exists
    if not Path("streamlit_app.py").exists():
        print("❌ streamlit_app.py not found!")
        print("   Please run this script from the project root directory")
        return False
    
    # Check if agent package can be imported
    try:
        import agent
        print("✅ Agent package available")
    except ImportError as e:
        print(f"❌ Agent package not available: {e}")
        return False
    
    # Check if streamlit is available
    try:
        import streamlit
        print(f"✅ Streamlit available (version {streamlit.__version__})")
    except ImportError:
        print("❌ Streamlit not installed")
        print("   Please install with: pip install streamlit")
        return False
    
    # Check if plotly is available
    try:
        import plotly
        print(f"✅ Plotly available (version {plotly.__version__})")
    except ImportError:
        print("❌ Plotly not installed")
        print("   Please install with: pip install plotly")
        return False
    
    # Check environment file
    if not Path(".env").exists():
        print("⚠️  .env file not found")
        print("   Make sure your database credentials are configured")
    else:
        print("✅ Environment file found")
    
    print("✅ All prerequisites met!")
    return True

def launch_streamlit():
    """Launch the Streamlit application"""
    print("\n🚀 LAUNCHING STREAMLIT APPLICATION...")
    print("📱 Opening in your default web browser...")
    print("🔗 URL: http://localhost:8501")
    print()
    print("⏹️  To stop the server, press Ctrl+C in this terminal")
    print("🔄 To restart, simply run this script again")
    print()
    
    try:
        # Launch streamlit in a subprocess
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
        
        # Wait a moment for the server to start
        time.sleep(3)
        
        # Try to open the browser
        try:
            webbrowser.open("http://localhost:8501")
            print("✅ Browser opened successfully!")
        except Exception as e:
            print(f"⚠️  Could not open browser automatically: {e}")
            print("   Please manually navigate to: http://localhost:8501")
        
        print("\n" + "=" * 50)
        print("🎉 STREAMLIT UI IS NOW RUNNING!")
        print("=" * 50)
        print("The Sherlock Agent UI is now available in your browser.")
        print("Follow the usage guide above to get started.")
        print("=" * 50)
        
        # Wait for the process to complete (user stops it)
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down Streamlit server...")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"\n❌ Error launching Streamlit: {e}")
        return False
    
    return True

def main():
    """Main demo function"""
    print_banner()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above and try again.")
        return 1
    
    # Show usage guide
    print_usage_guide()
    
    # Ask user if they want to continue
    print("\n🚀 Ready to launch the Streamlit UI?")
    response = input("Press Enter to continue or 'q' to quit: ").strip().lower()
    
    if response == 'q':
        print("👋 Demo cancelled. Run this script again when you're ready!")
        return 0
    
    # Launch the application
    success = launch_streamlit()
    
    if success:
        print("\n✅ Demo completed successfully!")
        print("💡 You can run 'python demo_ui.py' again anytime to restart the UI")
    else:
        print("\n❌ Demo encountered issues")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
