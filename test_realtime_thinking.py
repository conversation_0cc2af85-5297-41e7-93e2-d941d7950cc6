#!/usr/bin/env python3
"""
Test Real-time Thinking Display

This script tests the real-time thinking layer to ensure it properly
displays the current step as the agent executes.
"""

import time
import streamlit as st
from agent.thinking_layer import get_process_tracker
from agent.models import ThinkingStepType
from agent.live_thinking import show_thinking_status, create_live_thinking_display


def test_realtime_thinking():
    """Test the real-time thinking display"""
    st.title("🧠 Real-time Thinking Layer Test")
    
    if st.button("Start Test Process"):
        # Create a thinking process
        tracker = get_process_tracker()
        process_id = tracker.create_process(
            user_query="Test query for real-time display",
            session_id="test_session"
        )
        
        st.write(f"Created process: {process_id}")
        
        # Create display containers
        containers = create_live_thinking_display(process_id)
        
        # Simulate step execution with delays
        steps_to_execute = [
            (ThinkingStepType.QUERY_ANALYSIS, 2.0),
            (ThinkingStepType.SCHEMA_RETRIEVAL, 3.0),
            (ThinkingStepType.SQL_GENERATION, 2.5),
            (ThinkingStepType.QUERY_VALIDATION, 1.0),
            (ThinkingStepType.QUERY_EXECUTION, 4.0),
            (ThinkingStepType.RESULT_ANALYSIS, 1.5),
            (ThinkingStepType.RESPONSE_GENERATION, 1.0)
        ]
        
        for step_type, duration in steps_to_execute:
            # Start the step
            tracker.start_step(process_id, step_type)
            
            # Update display
            from agent.live_thinking import update_live_display
            update_live_display(process_id, containers)
            
            # Simulate processing time
            time.sleep(min(duration, 2.0))  # Cap at 2 seconds for demo
            
            # Complete the step
            tracker.complete_step(process_id, step_type, f"Completed {step_type.value}")
            
            # Update display again
            update_live_display(process_id, containers)
        
        # Complete the process
        tracker.complete_process(process_id)
        update_live_display(process_id, containers)
        
        st.success("Test completed!")


def test_simple_display():
    """Test simple current step display"""
    st.title("🔍 Simple Step Display Test")
    
    if st.button("Test Simple Display"):
        # Create a thinking process
        tracker = get_process_tracker()
        process_id = tracker.create_process(
            user_query="Simple display test",
            session_id="simple_test"
        )
        
        # Show different states
        st.subheader("1. Initial State")
        show_thinking_status(process_id, style="simple")
        
        # Start first step
        tracker.start_step(process_id, ThinkingStepType.QUERY_ANALYSIS)
        
        st.subheader("2. Step In Progress")
        show_thinking_status(process_id, style="current_step")
        
        # Complete first step
        tracker.complete_step(process_id, ThinkingStepType.QUERY_ANALYSIS, "Analysis complete")
        
        # Start second step
        tracker.start_step(process_id, ThinkingStepType.SCHEMA_RETRIEVAL)
        
        st.subheader("3. Next Step")
        show_thinking_status(process_id, style="current_step")
        
        # Complete all remaining steps
        tracker.complete_step(process_id, ThinkingStepType.SCHEMA_RETRIEVAL, "Schema retrieved")
        
        for step_type in [ThinkingStepType.SQL_GENERATION, ThinkingStepType.QUERY_VALIDATION, 
                         ThinkingStepType.QUERY_EXECUTION, ThinkingStepType.RESULT_ANALYSIS, 
                         ThinkingStepType.RESPONSE_GENERATION]:
            tracker.start_step(process_id, step_type)
            tracker.complete_step(process_id, step_type, f"Completed {step_type.value}")
        
        tracker.complete_process(process_id)
        
        st.subheader("4. Completed")
        show_thinking_status(process_id, style="simple")


def main():
    """Main test function"""
    st.set_page_config(
        page_title="Thinking Layer Test",
        page_icon="🧠",
        layout="wide"
    )
    
    st.sidebar.title("Test Options")
    test_type = st.sidebar.selectbox(
        "Select Test Type",
        ["Real-time Display", "Simple Display", "Both"]
    )
    
    if test_type == "Real-time Display":
        test_realtime_thinking()
    elif test_type == "Simple Display":
        test_simple_display()
    else:
        col1, col2 = st.columns(2)
        
        with col1:
            test_realtime_thinking()
        
        with col2:
            test_simple_display()


if __name__ == "__main__":
    main()
