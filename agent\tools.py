from langchain.tools.retriever import create_retriever_tool
from psycopg2.extras import RealDictCursor
from sqlalchemy.exc import OperationalError, ProgrammingError, DatabaseError
import pandas as pd
from agent.schema import load_schema_docs
from agent.vectorstore import create_vector_store
from agent.config import settings
from agent.db import connect_db, open_db_connection
from agent.logger import get_logger, log_database_operation, log_error
import time

# Get logger for this module
logger = get_logger(__name__)
from langchain_qdrant import QdrantVectorStore



def get_schema_retriever(collection_name: str, embeddings, **kwargs):
    """
    Connect to an existing Qdrant collection and return a retriever
    for use in LLM-based RAG pipelines.
    """
    vs = QdrantVectorStore.from_existing_collection(
        embedding=embeddings,
        collection_name=collection_name,
        url=settings.QDRANT_URL, api_key=settings.QDRANT_API_KEY,
    )
    retriever = vs.as_retriever(**kwargs)
    return retriever


def get_schema_retriever_tool(schema_retreiver):
    # schema_retreiver = vector_store.as_retriever(top_k=3)
    return create_retriever_tool(
        schema_retreiver,
        "schema_retreiver",
        "Search for relevant schemas and tables. This uses semantic search to find the most relevant schemas and tables"
    )

def get_schema_memory_retriever(vector_store):
    return vector_store.as_retriever(top_k=3)

def execute_sql_query(sql_query: str):
    """Execute SQL query with comprehensive logging"""
    start_time = time.time()
    logger.info(f"🔍 Executing SQL query: {sql_query[:100]}...")

    with open_db_connection() as conn:
        try:
            result = pd.read_sql_query(sql_query, conn)
            execution_time = time.time() - start_time
            row_count = len(result)

            logger.info(f"✅ SQL query executed successfully in {execution_time:.3f}s, returned {row_count} rows")
            log_database_operation(
                "sql_execute",
                query=sql_query[:200] + "..." if len(sql_query) > 200 else sql_query,
                success=True,
                execution_time=execution_time,
                row_count=row_count
            )

            return result.to_dict(orient="records")

        except (OperationalError, ProgrammingError, DatabaseError) as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ SQL query execution failed in {execution_time:.3f}s: {str(e)}")
            log_database_operation(
                "sql_execute",
                query=sql_query[:200] + "..." if len(sql_query) > 200 else sql_query,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
            log_error(e, "execute_sql_query", query=sql_query[:100])
            return {'error': str(e)}

# Removed module-level initialization to prevent circular imports
# These will be initialized lazily when needed

def get_default_vector_store():
    """Lazy initialization of vector store"""
    return create_vector_store(load_schema_docs(settings.SCHEMA_DEFINIOTIONS_PATH))


def get_default_schema_retriever():
    """Lazy initialization of schema retriever"""
    return get_schema_memory_retriever(get_default_vector_store())

def get_default_schema_retriever_tool():
    """Lazy initialization of schema retriever tool"""
    return get_schema_retriever_tool(get_schema_memory_retriever(get_default_vector_store()))

