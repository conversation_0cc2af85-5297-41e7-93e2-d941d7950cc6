#!/usr/bin/env python3
"""
Test Live Thinking Display

This script tests the live thinking layer to ensure it properly
displays the current step as the agent executes.
"""

import time
import streamlit as st
from agent.thinking_layer import get_process_tracker
from agent.models import ThinkingStepType
from agent.live_thinking import show_thinking_status, create_live_thinking_display
from agent.ui_components import render_current_step_display, render_thinking_process_card


def test_realtime_thinking():
    """Test the real-time thinking display"""
    st.title("🧠 Real-time Thinking Layer Test")
    
    if st.button("Start Test Process"):
        # Create a thinking process
        tracker = get_process_tracker()
        process_id = tracker.create_process(
            user_query="Test query for real-time display",
            session_id="test_session"
        )
        
        st.write(f"Created process: {process_id}")
        
        # Create display containers
        containers = create_live_thinking_display(process_id)
        
        # Simulate step execution with delays
        steps_to_execute = [
            (ThinkingStepType.QUERY_ANALYSIS, 2.0),
            (ThinkingStepType.SCHEMA_RETRIEVAL, 3.0),
            (ThinkingStepType.SQL_GENERATION, 2.5),
            (ThinkingStepType.QUERY_VALIDATION, 1.0),
            (ThinkingStepType.QUERY_EXECUTION, 4.0),
            (ThinkingStepType.RESULT_ANALYSIS, 1.5),
            (ThinkingStepType.RESPONSE_GENERATION, 1.0)
        ]
        
        for step_type, duration in steps_to_execute:
            # Start the step
            tracker.start_step(process_id, step_type)
            
            # Update display
            from agent.live_thinking import update_live_display
            update_live_display(process_id, containers)
            
            # Simulate processing time
            time.sleep(min(duration, 2.0))  # Cap at 2 seconds for demo
            
            # Complete the step
            tracker.complete_step(process_id, step_type, f"Completed {step_type.value}")
            
            # Update display again
            update_live_display(process_id, containers)
        
        # Complete the process
        tracker.complete_process(process_id)
        update_live_display(process_id, containers)
        
        st.success("Test completed!")


def test_simple_display():
    """Test simple current step display"""
    st.title("🔍 Simple Step Display Test")
    
    if st.button("Test Simple Display"):
        # Create a thinking process
        tracker = get_process_tracker()
        process_id = tracker.create_process(
            user_query="Simple display test",
            session_id="simple_test"
        )
        
        # Show different states
        st.subheader("1. Initial State")
        show_thinking_status(process_id, style="simple")
        
        # Start first step
        tracker.start_step(process_id, ThinkingStepType.QUERY_ANALYSIS)
        
        st.subheader("2. Step In Progress")
        show_thinking_status(process_id, style="current_step")
        
        # Complete first step
        tracker.complete_step(process_id, ThinkingStepType.QUERY_ANALYSIS, "Analysis complete")
        
        # Start second step
        tracker.start_step(process_id, ThinkingStepType.SCHEMA_RETRIEVAL)
        
        st.subheader("3. Next Step")
        show_thinking_status(process_id, style="current_step")
        
        # Complete all remaining steps
        tracker.complete_step(process_id, ThinkingStepType.SCHEMA_RETRIEVAL, "Schema retrieved")
        
        for step_type in [ThinkingStepType.SQL_GENERATION, ThinkingStepType.QUERY_VALIDATION, 
                         ThinkingStepType.QUERY_EXECUTION, ThinkingStepType.RESULT_ANALYSIS, 
                         ThinkingStepType.RESPONSE_GENERATION]:
            tracker.start_step(process_id, step_type)
            tracker.complete_step(process_id, step_type, f"Completed {step_type.value}")
        
        tracker.complete_process(process_id)
        
        st.subheader("4. Completed")
        show_thinking_status(process_id, style="simple")


def test_ui_components():
    """Test UI components"""
    st.title("🎨 UI Components Test")
    
    if st.button("Test UI Components"):
        # Create a thinking process
        tracker = get_process_tracker()
        process_id = tracker.create_process(
            user_query="UI components test",
            session_id="ui_test"
        )
        
        # Test current step display
        st.subheader("Current Step Display")
        step_placeholder = st.empty()
        
        # Simulate steps
        for step_type in [ThinkingStepType.QUERY_ANALYSIS, ThinkingStepType.SCHEMA_RETRIEVAL, 
                         ThinkingStepType.SQL_GENERATION]:
            tracker.start_step(process_id, step_type)
            render_current_step_display(process_id, step_placeholder)
            time.sleep(1)
            tracker.complete_step(process_id, step_type, f"Completed {step_type.value}")
        
        # Test process card
        st.subheader("Process Card")
        render_thinking_process_card(process_id, compact=True)
        
        tracker.complete_process(process_id)


def test_collapsible_sections():
    """Test collapsible sections"""
    st.title("📁 Collapsible Sections Test")
    
    # Test SQL query display
    with st.expander("📝 SQL Query Example", expanded=False):
        st.code("""
        SELECT 
            farmer_id,
            name,
            location,
            total_loans
        FROM farmers 
        WHERE status = 'active'
        ORDER BY total_loans DESC
        LIMIT 10;
        """, language="sql")
    
    # Test data results display
    with st.expander("📊 Data Results Example", expanded=False):
        import pandas as pd
        sample_data = {
            'farmer_id': [1, 2, 3, 4, 5],
            'name': ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Charlie Wilson'],
            'location': ['Lagos', 'Abuja', 'Kano', 'Port Harcourt', 'Ibadan'],
            'total_loans': [50000, 75000, 30000, 90000, 45000]
        }
        df = pd.DataFrame(sample_data)
        st.dataframe(df, use_container_width=True)
        
        # Sample chart
        import plotly.express as px
        fig = px.bar(df, x='name', y='total_loans', title='Farmer Loan Amounts')
        st.plotly_chart(fig, use_container_width=True)
    
    # Test thinking process display
    with st.expander("🧠 Thinking Process Example", expanded=False):
        st.info("🔍 Analyzing user query...")
        st.success("✅ Query analysis complete")
        st.info("📊 Retrieving database schema...")
        st.success("✅ Schema retrieval complete")
        st.info("⚡ Generating SQL query...")


def main():
    """Main test function"""
    st.set_page_config(
        page_title="Live Thinking Test",
        page_icon="🧠",
        layout="wide"
    )
    
    st.sidebar.title("Test Options")
    test_type = st.sidebar.selectbox(
        "Select Test Type",
        ["Real-time Display", "Simple Display", "UI Components", "Collapsible Sections", "All Tests"]
    )
    
    if test_type == "Real-time Display":
        test_realtime_thinking()
    elif test_type == "Simple Display":
        test_simple_display()
    elif test_type == "UI Components":
        test_ui_components()
    elif test_type == "Collapsible Sections":
        test_collapsible_sections()
    else:
        col1, col2 = st.columns(2)
        
        with col1:
            test_realtime_thinking()
            test_ui_components()
        
        with col2:
            test_simple_display()
            test_collapsible_sections()


if __name__ == "__main__":
    main()
