[["SH_exchange_mart.fact_prices_aci", "Table Name: `exchange_mart.fact_prices_aci`\nThis table is attributed to Africa Exchange System. \nTable description: Aggregated Commodity Index (ACI) capturing commodity price movements on the Africa Exchange.\nIt contains the following columns:\n\tname: date\n\tdata_type: date\n\tdescription: The date of the price index record.\n\n\tname: commodity_code\n\tdata_type: text\n\tdescription: Unique code representing the commodity that makes up the index\n\n\tname: commodity\n\tdata_type: text\n\tdescription: Name of the commodity that makes up the index\n\n\tname: commodity_weight\n\tdata_type: double precision\n\tdescription: Percentage contribution of the commodity to the index\n\n\tname: closing_price_index_mt\n\tdata_type: double precision\n\tdescription: Closing index price of the commodity\n\n\tname: points\n\tdata_type: double precision\n\tdescription: The actual index value. For the commodity, index point of the commodity or sub_index value.\n\n\tname: prev_day_point\n\tdata_type: double precision\n\tdescription: Index points value from the previous day.\n\n\tname: dod_change\n\tdata_type: double precision\n\tdescription: Day-over-day percentage change in the commodity index.\n\n\tname: prev_week_point\n\tdata_type: double precision\n\tdescription: Index points value from the previous week.\n\n\tname: wow_change\n\tdata_type: double precision\n\tdescription: Week-over-week percentage change in the commodity index.\n\n\tname: week_start\n\tdata_type: double precision\n\tdescription: Index value at the start of the current week.\n\n\tname: week_end\n\tdata_type: double precision\n\tdescription: Index value at the end of the current week.\n\n\tname: month_start\n\tdata_type: double precision\n\tdescription: Index value at the start of the current month.\n\n\tname: month_end\n\tdata_type: double precision\n\tdescription: Index value at the end of the current month.\n\n\tname: mtd_change\n\tdata_type: double precision\n\tdescription: Month-to-date percentage change in the commodity index.\n\n\tname: previous_month_end\n\tdata_type: double precision\n\tdescription: Index value at the end of the previous month.\n\n\tname: mom_change\n\tdata_type: double precision\n\tdescription: Month-over-month percentage change in the commodity index.\n\n\tname: quarter_start\n\tdata_type: double precision\n\tdescription: Index value at the start of the current quarter.\n\n\tname: quarter_end\n\tdata_type: double precision\n\tdescription: Index value at the end of the current quarter.\n\n\tname: qtd_change\n\tdata_type: double precision\n\tdescription: Quarter-to-date percentage change in the commodity index.\n\n\tname: previous_quarter_end\n\tdata_type: double precision\n\tdescription: Index value at the end of the previous quarter.\n\n\tname: qoq_change\n\tdata_type: double precision\n\tdescription: Quarter-over-quarter percentage change in the commodity index.\n\n\tname: season_start\n\tdata_type: double precision\n\tdescription: Index value at the start of the seasonal period.\n\n\tname: std_change\n\tdata_type: double precision\n\tdescription: Season-to-date percentage change in the commodity index.\n\n\tname: year_start\n\tdata_type: double precision\n\tdescription: Index value at the start of the current year.\n\n\tname: ytd_change\n\tdata_type: double precision\n\tdescription: Year-to-date percentage change in the commodity index.\n\nHere are some relevant example rows (values in the same order as columns above)\n(datetime.datetime(2025, 6, 27, 0, 0), 'ACI', 'AFEX Commodities Index', None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 690.1388703461239, None, None, N"], ["SH_exchange_mart.fact_prices_aei", "Table Name: `exchange_mart.fact_prices_aei`\nThis table is attributed to Africa Exchange System. \nTable description: Aggregated Exchange Index (AEI) capturing commodity price movements on the Africa Exchange.\nIt contains the following columns:\n\tname: date\n\tdata_type: date\n\tdescription: The trading date.\n\n\tname: commodity_code\n\tdata_type: text\n\tdescription: Unique code representing the traded commodity that makes up the index\n\n\tname: commodity\n\tdata_type: text\n\tdescription: Name of the traded commodity that makes up the index\n\n\tname: commodity_weight\n\tdata_type: double precision\n\tdescription: Percentage contribution of the commodity to the index\n\n\tname: closing_price_index_mt\n\tdata_type: double precision\n\tdescription: Closing index price of the commodity\n\n\tname: points\n\tdata_type: double precision\n\tdescription: Index points for the commodity.\n\n\tname: prev_day_point\n\tdata_type: double precision\n\tdescription: Previous day's index points.\n\n\tname: dod_change\n\tdata_type: double precision\n\tdescription: Day-over-day percentage change in the index.\n\n\tname: prev_week_point\n\tdata_type: double precision\n\tdescription: Previous week's index points.\n\n\tname: wow_change\n\tdata_type: double precision\n\tdescription: Week-over-week percentage change in the index.\n\n\tname: week_start\n\tdata_type: double precision\n\tdescription: Index at the start of the current week.\n\n\tname: week_end\n\tdata_type: double precision\n\tdescription: Index at the end of the current week.\n\n\tname: month_start\n\tdata_type: double precision\n\tdescription: Index at the start of the current month.\n\n\tname: month_end\n\tdata_type: double precision\n\tdescription: Index at the end of the current month.\n\n\tname: mtd_change\n\tdata_type: double precision\n\tdescription: Month-to-date percentage change in the index.\n\n\tname: previous_month_end\n\tdata_type: double precision\n\tdescription: Index at the end of the previous month.\n\n\tname: mom_change\n\tdata_type: double precision\n\tdescription: Month-over-month percentage change in the index.\n\n\tname: quarter_start\n\tdata_type: double precision\n\tdescription: Index at the start of the current quarter.\n\n\tname: quarter_end\n\tdata_type: double precision\n\tdescription: Index at the end of the current quarter.\n\n\tname: qtd_change\n\tdata_type: double precision\n\tdescription: Quarter-to-date percentage change in the index.\n\n\tname: previous_quarter_end\n\tdata_type: double precision\n\tdescription: Index at the end of the previous quarter.\n\n\tname: qoq_change\n\tdata_type: double precision\n\tdescription: Quarter-over-quarter percentage change in the index.\n\n\tname: season_start\n\tdata_type: double precision\n\tdescription: Index at the start of the seasonal period.\n\n\tname: std_change\n\tdata_type: double precision\n\tdescription: Season-to-date percentage change in the index.\n\n\tname: year_start\n\tdata_type: double precision\n\tdescription: Index at the start of the current year.\n\n\tname: ytd_change\n\tdata_type: double precision\n\tdescription: Year-to-date percentage change in the index.\n\nHere are some relevant example rows (values in the same order as columns above)\n(datetime.datetime(2025, 6, 27, 0, 0), 'AEI', 'AFEX Export Index', None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 1082.2323587074561, None, None, None)\n(datetime.datetime(2025, 6, 27, 0, 0), 'COC', 'Cocoa', None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, 1357.5174772899886, None, None, None)\n(datetime.datetime(2025"], ["SH_exchange_mart.fact_prices_commodities_prices", "Table Name: `exchange_mart.fact_prices_commodities_prices`\nThis table is attributed to Africa Exchange System. \nTable description: Daily closing prices of commodities on the exchange\nIt contains the following columns:\n\tname: date\n\tdata_type: Date\n\tdescription: Trading date\n\n\tname: dow\n\tdata_type: integer\n\tdescription: Day of the week (0-6)\n\n\tname: woy\n\tdata_type: text\n\tdescription: Week of the year. Example 2025_8\n\n\tname: quarter\n\tdata_type: text\n\tdescription: Quarter of the year. Example 2025_Q1\n\n\tname: commodity_code\n\tdata_type: text\n\tdescription: Unique commodity identifier\n\n\tname: commodity_name\n\tdata_type: text\n\tdescription: Commodity name\n\n\tname: closing_price_kg\n\tdata_type: double precision\n\tdescription: Closing price of the commodity per kilogram\n\n\tname: opening_price_kg\n\tdata_type: double precision\n\tdescription: Opening price of the commodity per kilogram\n\n\tname: max_price_kg\n\tdata_type: double precision\n\tdescription: Maximum price per kilogram. This is the Maximum price that security sold for on that day. When no transaction, this is the same as closing price.\n\n\tname: min_price_kg\n\tdata_type: double precision\n\tdecription: Minimum price per kilogram. This is the Minimum price that security sold for on that day. When no transaction, this is the same as closing price.\n\n\tname: dod_price_dif\n\tdata_type: double precision\n\tdescription: Day-over-day closing price difference\n\n\tname: dod_price_change\n\tdata_type: double precision\n\tdescription: Day-over-day percentage change\n\n\tname: week_start_price\n\tdata_type: double precision\n\tdescription: Opening price of the week. Week starts 15:00:00 Monday\n\n\tname: week_end_price\n\tdata_type: double precision\n\tdescription: Closing price of the week. Week ends 14:59:59 Friday\n\n\tname: wtd_price_change\n\tdata_type: double precision\n\tdescription: Week-to-date percentage change\n\n\tname: previous_week_price\n\tdata_type: double precision\n\tdescription: Closing price of the previous week\n\n\tname: wow_price_change\n\tdata_type: double precision\n\tdescription: Week-over-week percentage change\n\n\tname: month_start_price\n\tdata_type: double precision\n\tdescription: Opening price on first day of month\n\n\tname: month_end_price\n\tdata_type: double precision\n\tdescription: Closing price on last day of month\n\n\tname: mtd_price_change\n\tdata_type: double precision\n\tdescription: Month-to-date percentage change\n\n\tname: previous_month_price\n\tdata_type: double precision\n\tdescription: Closing price of the previous month\n\n\tname: mom_price_change\n\tdata_type: double precision\n\tdescription: Month-over-month percentage change\n\n\tname: previous_month_end_price\n\tdata_type: double precision\n\tdescription: Closing price of previous month\n\n\tname: month_end_price_change\n\tdata_type: double precision\n\tdescription: Month-end to month-end percentage change. This is the price change between this month end the previous month end\n\n\tname: quarter_start_price\n\tdata_type: double precision\n\tdescription: Opening price of current quarter\n\n\tname: quarter_end_price\n\tdata_type: double precision\n\tdescription: Closing price of current quarter\n\n\tname: qtd_price_change\n\tdata_type: double precision\n\tdescription: Quarter-to-date percentage change\n\n\tname: previous_quarter_price\n\tdata_type: double precision\n\tdescription: Closing price of previous quarter\n\n\tname: qoq_price_change\n\tdata_type: double precision\n\tdescription: Quarter-over-quarter percentage change\n\n\tname: previous_quarter_end_price\n\tdata_type: double precision\n\tdescription: Closing price of previous quarter\n\n\tname"], ["SH_exchange_mart.fact_prices_securities_prices", "Table Name: `exchange_mart.fact_prices_securities_prices`\nThis table is attributed to Africa Exchange System. \nTable description: Daily closing prices of securities on the exchange\nIt contains the following columns:\n\tname: date\n\tdata_type: Date\n\tdescription: Trading date\n\n\tname: dow\n\tdata_type: integer\n\tdescription: Day of the week (0-6)\n\n\tname: woy\n\tdata_type: text\n\tdescription: Week of the year. Example 2025_8\n\n\tname: season\n\tdata_type: text\n\tdescription: Season of the year\n\n\tname: quarter\n\tdata_type: text\n\tdescription: Quarter of the year. Example 2025_Q1\n\n\tname: security_code\n\tdata_type: text\n\tdescription: Unique security identifier\n\n\tname: security_name\n\tdata_type: text\n\tdescription: Security name\n\n\tname: security_type\n\tdata_type: text\n\tdescription: Security type. This typically the board type of the security ('OTC', 'Spot')\n\n\tname: closing_price_kg\n\tdata_type: double precision\n\tdescription: Closing price of the commodity per kilogram\n\n\tname: closing_price_per_unit\n\tdata_type: double precision\n\tdescription: Closing price of the commodity per unit\n\n\tname: opening_price_kg\n\tdata_type: double precision\n\tdescription: Opening price of the commodity per kilogram\n\n\tname: opening_price_per_unit\n\tdata_type: double precision\n\tdescription: Opening price of the commodity per unit\n\n\tname: max_price_kg\n\tdata_type: double precision\n\tdescription: Maximum price per kilogram. This is the Maximum price that security sold for on that day. When no transaction, this is the same as closing price.\n\n\tname: max_price_per_unit\n\tdata_type: double precision\n\tdescription: Maximum price per unit\n\n\tname: min_price_kg\n\tdata_type: double precision\n\tdecription: Minimum price per kilogram. This is the Minimum price that security sold for on that day. When no transaction, this is the same as closing price.\n\n\tname: min_price_per_unit\n\tdata_type: double precision\n\tdecription: Minimum price per unit\n\n\tname: dod_price_dif\n\tdata_type: double precision\n\tdescription: Day-over-day closing price difference\n\n\tname: dod_price_change\n\tdata_type: double precision\n\tdescription: Day-over-day percentage change\n\n\tname: week_start_price\n\tdata_type: double precision\n\tdescription: Opening price of the week.\n\n\tname: week_end_price\n\tdata_type: double precision\n\tdescription: Closing price of the week. Week ends 14:59:59 Friday\n\n\tname: wtd_price_change\n\tdata_type: double precision\n\tdescription: Week-to-date percentage change\n\n\tname: previous_week_price\n\tdata_type: double precision\n\tdescription: Closing price of the previous week\n\n\tname: wow_price_change\n\tdata_type: double precision\n\tdescription: Week-over-week percentage change\n\n\tname: month_start_price\n\tdata_type: double precision\n\tdescription: Opening price on first day of month\n\n\tname: month_end_price\n\tdata_type: double precision\n\tdescription: Closing price on last day of month\n\n\tname: mtd_price_change\n\tdata_type: double precision\n\tdescription: Month-to-date percentage change\n\n\tname: previous_month_price\n\tdata_type: double precision\n\tdescription: Closing price of the previous month\n\n\tname: mom_price_change\n\tdata_type: double precision\n\tdescription: Month-over-month percentage change\n\n\tname: previous_month_end_price\n\tdata_type: double precision\n\tdescription: Closing price of previous month\n\n\tname: month_end_price_change\n\tdata_type: double precision\n\tdescription: Month-end to month-end percentage change. That is the price change between this month end the previous month end.\n\n\tname: quarter_start_price\n\tdata_type: double precision\n\tdescript"], ["SH_exchange_mart.fact_trade_individual_transactions", "Table Name: `exchange_mart.fact_trade_individual_transactions`\nThis table is attributed to Africa Exchange System. \nTable description: \nIt contains the following columns:\n\tname: execution_id\n\tdata_type: text\n\tdescription: Identifier for transaction execution. This tags otc trades per delivery\n\n\tname: deal_id\n\tdata_type: text\n\tdescription: Identifier for a fulfiled deal. This tags otc trades per day\n\n\tname: executed_date\n\tdata_type: date\n\tdescription: Date the transaction was executed. When the non-otc order was matched or the otc order was delivered.\n\n\tname: executed_volume_kg\n\tdata_type: double precision\n\tdescription: Total volume that was delivered in that transaction\n\n\tname: execution_created_at\n\tdata_type: timestamp with time zone\n\tdescription: This is date when the non-otc transaction was first matched or when the otc dispatch was done\n\n\tname: execution_updated_at\n\tdata_type: timestamp with time zone\n\tdescription: When transaction was updated\n\n\tname: trade_created_at\n\tdata_type: timestamp with time zone\n\tdescription: This the date the client first made the order - order creation\n\n\tname: trade_updated_at\n\tdata_type: timestamp with time zone\n\tdescription: When the initial order was updated\n\n\tname: deal_created_at\n\tdata_type: timestamp with time zone\n\tdescription: Date the order was first matched\n\n\tname: adjusted_deal_created_at\n\tdata_type: date\n\tdescription: Deal created date adjust to timezone WAT\n\n\tname: deal_updated_at\n\tdata_type: timestamp with time zone\n\tdescription: The last time the deal was updated i.e the last time the matched order was updated\n\n\tname: deal_processed_at\n\tdata_type: timestamp with time zone\n\tdescription: When the matched order was first processed\n\n\tname: trade_is_on_behalf\n\tdata_type: boolean\n\tdescription: Is the trade on behalf of another client\n\n\tname: trade_status\n\tdata_type: text\n\tdescription: Status of the trade. Is the trade Canceled, Market Canceled, Partial or Pending\n\n\tname: is_order_cancelled\n\tdata_type: boolean\n\tdescription: Flag to show if order has been canceled\n\n\tname: trade_is_rejected\n\tdata_type: boolean\n\tdescription: Flag to show if traded is rejected\n\n\tname: tid\n\tdata_type: text\n\tdescription: Unique transaction identifier. This id however is not unique on this table\n\n\tname: trans_cid\n\tdata_type: text\n\tdescription: Client CID associated with the order request\n\n\tname: order_type\n\tdata_type: text\n\tdescription: Type of order\n\n\tname: matched_id\n\tdata_type: text\n\tdescription: Unique trade match identifier\n\n\tname: sell_tid\n\tdata_type: text\n\tdescription: tid of the seller's transaction\n\n\tname: buy_tid\n\tdata_type: text\n\tdescription: tid of the buyer's transaction\n\n\tname: seller_cid\n\tdata_type: text\n\tdescription: Unique cid of the seller\n\n\tname: buyer_cid\n\tdata_type: text\n\tdescription: Unique cid of the buyer\n\n\tname: volume_per_unit\n\tdata_type: bigint\n\tdescription: Volume of commodity in a unit of the commodity on the exchange\n\n\tname: calc_volume_per_unit\n\tdata_type: bigint\n\tdescription: Calculated volume of commodity in a unit of the commodity\n\n\tname: currency\n\tdata_type: text\n\tdescription: Currency which is being used in that transaction\n\n\tname: order_units\n\tdata_type: bigint\n\tdescription: Total number of units raised by the order\n\n\tname: order_price\n\tdata_type: double precision\n\tdescription: Bid price for the commodity\n\n\tname: matched_units\n\tdata_type: bigint\n\tdescription: Total number of units that was matched from the order units under this tid\n\n\tname: matched_price\n\tdata_type: double precision\n\t"], ["SH_exchange_mart.dim_clients_segments", "Table Name: `exchange_mart.dim_clients_segments`\nThis table is attributed to Africa Exchange System. \nTable description: This a dimension table that stores the segments of each clients and their trading attributes\nIt contains the following columns:\n\tname: cid\n\tdata_type: text\n\tdescription: Unique cid of client\n\n\tname: last_seen\n\tdata_type: datetime\n\tdescription: Datetime showing the last time a client was last active on the exchange\n\n\tname: time_since_last_seen\n\tdata_type: interval\n\tdescription: Time that has passed since the client was last seen online\n\n\tname: last_action_time\n\tdata_type: datetime\n\tdescription: Datetime showing the last time the client made an action e.g trade, fund wallet\n\n\tname: time_since_last_action\n\tdata_type: interval\n\tdescription: Time that passed since the client made an action on the exchange\n\n\tname: active_status\n\tdata_type: text\n\tdescription: The engagement level of client. This expected to be ('Active', 'Passive', 'Dormant')\n\n\tname: best_notification_hour\n\tdata_type: integer\n\tdescription: The hour of the day (24-hour) the client is most active. This taken as the best time to send the client notification\n\n\tname: activity_hour_frequency\n\tdata_type: integer\n\tdescription: The number of times the user has been seen online during their best_notification_hour\n\n\tname: avg_online_duration_within_hour\n\tdata_type: float\n\tdescription: The average duration seconds the client stayed online at their best_notification time\n\n\tname: boards\n\tdata_type: json\n\tdescription: This is a json aggregate of the boards the user have traded and the number of times they traded each board\n\n\tname: commodities\n\tdata_type: json\n\tdescription: This is a json aggregate of the commodities the user have ever traded and the number of times they traded each commodity\"\n\nHere are some relevant example rows (values in the same order as columns above)\n('5000000033', datetime.datetime(2024, 7, 3, 9, 11, 10, 439739, tzinfo=datetime.timezone.utc), datetime.timedelta(days=354), datetime.datetime(2024, 7, 3, 9, 11, 10, 439739, tzinfo=datetime.timezone.utc), 'Login', datetime.timedelta(days=354), 'Dormant', datetime.time(9, 0), 1, datetime.timedelta(seconds=500, microseconds=465676), None, None)\n('5000000034', datetime.datetime(2025, 1, 8, 9, 11, 48, 380808, tzinfo=datetime.timezone.utc), datetime.timedelta(days=169), datetime.datetime(2025, 1, 8, 9, 11, 48, 380808, tzinfo=datetime.timezone.utc), 'Login', datetime.timedelta(days=169), 'Dormant', datetime.time(9, 0), 2, datetime.timedelta(seconds=317, microseconds=598160), None, None)\n('5000000035', datetime.datetime(2024, 7, 10, 13, 49, 38, 879057, tzinfo=datetime.timezone.utc), datetime.timedelta(days=346), datetime.datetime(2024, 7, 10, 13, 49, 38, 879057, tzinfo=datetime.timezone.utc), 'Login', datetime.timedelta(days=346), 'Dormant', datetime.time(13, 0), 1, datetime.timedelta(seconds=946, microseconds=97512), None, None)\n"], ["SH_exchange_mart.dim_client", "Table Name: `exchange_mart.dim_client`\nThis table is attributed to Africa Exchange System. \nTable description: contains records on clients personal details like registeration and contact details for the comx platform and not the workbench platform\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription:  The unique identifier of the client.\n\n\tname: cid\n\tdata_type: text\n\tdescription:  The unique identifier associated with the client that ref comx_mart.dim_client.cid\n\n\tname: rnb\n\tdata_type: text\n\tdescription:  The rnb unique identifier associated with the client.\n\n\tname: email\n\tdata_type: text\n\tdescription:  The email address of the client.\n\n\tname: phone\n\tdata_type: text\n\tdescription:  The phone number of the client.\n\n\tname: address\n\tdata_type: text\n\tdescription:  The address of the client.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription:  Timestamp indicating when the client record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription:  Timestamp indicating the last update time for this client record.\n\n\tdata_type: text\n\tdescription:  The last name of the client.\n\n\tdata_type: text\n\tdescription:  The first name of the client.\n\n\tname: share_code\n\tdata_type: text\n\tdescription:  The share code associated with the client.\n\n\tname: is_approved\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client is approved.\n\n\tname: account_type\n\tdata_type: text\n\tdescription:  The type of account associated with the client(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r, Trader, Logistic Partner, Client, Investor).\n\n\tname: is_certified\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client is certified.\n\n\tname: is_synced_wb\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client is synced with WB (World Bank).\n\n\tname: referral_code\n\tdata_type: text\n\tdescription:  The referral code associated with the client.\n\n\tname: verify_me_dob\n\tdata_type: text\n\tdescription:  The date of birth returned by VerifyMe during verification \n\n\tname: is_afex_broker\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client is an AFEX broker.\n\n\tname: is_id_verified\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client's identity is verified.\n\n\tname: is_bvn_verified\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client's BVN (Bank Verification Number) is verified.\n\n\tname: is_kyc_complete\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client's KYC (Know Your Customer) process is complete.\n\n\tname: is_kyc_rejected\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client's KYC is rejected.\n\n\tname: is_kyc_verified\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client's KYC is verified.\n\n\tname: client_broker_id\n\tdata_type: bigint\n\tdescription:  The unique identifier of the broker associated with the client.\n\n\tname: is_kyc_submitted\n\tdata_type: boolean\n\tdescription:  A flag indicating whether the client's KYC is submitted.\n\n\tname: is_update_pending\n\tdata_type: boolean\n\tdescription:  A flag indicating whether there are pending updates for the client.\n\n\tname: user_account_type\n\tdata_type: text\n\tdescription: The type of user account associated with the client (Individual, Corporate)\n\n\tname: bvn_error_response\n\tdata_type: text\n\tdescription:  The error response associated with BVN verification.\n\n\tname: used_referral_code\n\tdata_type: text\n\tdescription: The referral code used by the client.\n\n\t"], ["SH_exchange_mart.dim_security", "Table Name: `exchange_mart.dim_security`\nThis table is attributed to Africa Exchange System. \nTable description: Dimension table for all securities traded on the exchange\nIt contains the following columns:\n\tname: id\n\tdata_type: integer\n\tdescription: Table primary key\n\n\tname: security_code\n\tdata_type: text\n\tdescription: Unique identifier of the security\n\n\tname: security_name\n\tdata_type: text\n\tdescription: Security name\n\n\tname: sec_security_type\n\tdata_type: text\n\tdescription: Security type based on SEC (Security Exchange Commission)\n\n\tname: security_type\n\tdata_type: text\n\tdescription: Security type\n\n\tname: board_name\n\tdata_type: text\n\tdescription: Board name of the security\n\n\tname: volume_per_unit\n\tdata_type: bigint\n\tdescription: Amount of the security in one unit\n\n\tname: commodity_code\n\tdata_type: text\n\tdescription: Unique identifier of the unsecuritized commodity\n\n\tname: commodity_name\n\tdata_type: text\n\tdescription: Name of the commodity\n\n\tname: currency_code\n\tdata_type: text\n\tdescription: Currency code\n\n\tname: currency_name\n\tdata_type: text\n\tdescription: Currency name\n\n\tname: is_virtual_security\n\tdata_type: boolean\n\tdescription: A flag to indicate if the security is virtual security on the exchange\n\nHere are some relevant example rows (values in the same order as columns above)\n(164, 'ECGE', 'CASSAVA', 'FI', 'Derivatives', 1000, 'CGE', '<PERSON>ava gar<PERSON>', None, None, False, datetime.datetime(2024, 5, 7, 14, 54, 27, 872962, tzinfo=datetime.timezone.utc))\n(175, 'COCF', 'Cocoa Forwards', 'FI', 'Derivatives', 1000, 'COC', 'Cocoa', 'NGN', 'Naira', False, datetime.datetime(2024, 7, 24, 9, 47, 3, 122969, tzinfo=datetime.timezone.utc))\n(156, 'eth', 'der', 'FI', 'Derivatives', 1000, 'SBS', 'Soyabean', 'NGN', 'Naira', False, datetime.datetime(2024, 4, 30, 16, 23, 6, 144554, tzinfo=datetime.timezone.utc))\n"], ["SH_trade_mart.fact_loan", "Table Name: `trade_mart.fact_loan`\nThis table is attributed to Workbench System. \nTable description: contains loan records\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the loan record.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the loan record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this loan record.\n\n\tname: loan_bundlename\n\tdata_type: text\n\tdescription: The bundle name associated with the loan.\n\n\tname: created_offline\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the loan record was created offline.\n\n\tname: approval_date\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the loan was approved.\n\n\tname: farmer_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the farmer associated with the loan.\n\n\tname: project_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the project associated with the loan.\n\n\tname: project_start_date\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the start date of the project associated with the loan.\n\n\tname: project_name\n\tdata_type: text\n\tdescription: The name of the loan project. Projects for wet season loans contains 'wet'.\n\n\tname: project_code\n\tdata_type: text\n\tdescription: The code representing the loan project.\n\n\tname: maturity_date\n\tdata_type: date\n\tdescription: The maturity date of the loan.\n\n\tname: warehouse_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the warehouse associated with the loan.\n\n\tname: warehouse_name\n\tdata_type: text\n\tdescription: The name of the warehouse associated with the loan.\n\n\tname: data_identification_verification\n\tdata_type: double precision\n\tdescription: \n\n\tname: tenant_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the tenant  associated with the loan.\n\n\tname: ln_id\n\tdata_type: text\n\tdescription: The loan ID \n\n\tname: hectare\n\tdata_type: bigint\n\tdescription: The area of the farm in hectares for which the loan is provided.\n\n\tname: total_loan_value\n\tdata_type: double precision\n\tdescription: The total value of the loan.\n\n\tname: repayment_value\n\tdata_type: double precision\n\tdescription: The repayment value of the loan.\n\n\tname: amount_repaid\n\tdata_type: double precision\n\tdescription: The amount that has been repaid for the loan.\n\n\tname: insurance\n\tdata_type: double precision\n\tdescription: The insurance amount associated with the loan.\n\n\tname: crg\n\tdata_type: double precision\n\tdescription: The credit risk guarantee amount for the loan.\n\n\tname: interest\n\tdata_type: double precision\n\tdescription: The interest amount for the loan.\n\n\tname: admin_fee\n\tdata_type: double precision\n\tdescription: The administrative fee associated with the loan.\n\n\tname: equity\n\tdata_type: double precision\n\tdescription: The equity amount for the loan.\n\n\tname: to_balance\n\tdata_type: double precision\n\tdescription: The balance amount remaining on the loan.\n\n\tname: loan_status\n\tdata_type: text\n\tdescription: \n\n\tname: is_repaid\n\tdata_type: boolean\n\tdescription: A flag indicating whether the loan is fully repaid. always use this to check if a loan has been paid back\n\n\tname: is_approved\n\tdata_type: boolean\n\tdescription: A flag indicating whether the loan is approved.\n\n\tname: is_approval_completed\n\tdata_type: boolean\n\tdescription: A flag indicating whether the approval process for the loan is completed"], ["SH_trade_mart.fact_loan_breakdown", "Table Name: `trade_mart.fact_loan_breakdown`\nThis table is attributed to Workbench System. \nTable description: contains a breakdown of loan componets contained in fact_loan table\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the loan breakdown record.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the loan breakdown record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this loan breakdown record.\n\n\tname: maturity_date\n\tdata_type: timestamp with time zone\n\tdescription: The maturity date of the loan breakdown\n\n\tname: farmer_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the farmer associated with the loan breakdown ref dim_farmer.id\n\n\tname: project_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the project associated with the loan breakdown.\n\n\tname: warehouse_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the warehouse associated with the loan breakdown ref dim_warehouse.id\n\n\tname: item_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the item associated with the loan breakdown ref dim_item.id\n\n\tname: tenant_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the tenant  associated with the loan breakdown.\n\n\tname: ln_id\n\tdata_type: text\n\tdescription: The loan ID associated with the loan breakdown.\n\n\tname: line_id\n\tdata_type: text\n\tdescription: The line ID associated with the loan breakdown.\n\n\tname: hectare\n\tdata_type: bigint\n\tdescription: The area of the farm in hectares for which the loan breakdown is provided.\n\n\tname: units\n\tdata_type: bigint\n\tdescription: The number of units associated with the loan .\n\n\tname: unit_price\n\tdata_type: double precision\n\tdescription: The unit price associated with the loan breakdown.\n\n\tname: total_price\n\tdata_type: double precision\n\tdescription: The total price associated with the loan breakdown.\n\n\tname: total_loan_value\n\tdata_type: double precision\n\tdescription: The total value of the loan breakdown.\n\n\tname: repayment_value\n\tdata_type: double precision\n\tdescription: The repayment value associated with the loan breakdown.\n\n\tname: amount_repaid\n\tdata_type: double precision\n\tdescription: The amount repaid for the loan breakdown.\n\n\tname: insurance\n\tdata_type: double precision\n\tdescription: The insurance amount associated with the loan breakdown.\n\n\tname: crg\n\tdata_type: double precision\n\tdescription: The credit risk guarantee amount for the loan breakdown.\n\n\tname: interest\n\tdata_type: double precision\n\tdescription: The interest amount associated with the loan breakdown.\n\n\tname: admin_fee\n\tdata_type: double precision\n\tdescription: The administrative fee associated with the loan breakdown.\n\n\tname: equity\n\tdata_type: double precision\n\tdescription: The equity amount associated with the loan breakdown.\n\n\tname: to_balance\n\tdata_type: double precision\n\tdescription: The balance amount remaining on the loan breakdown.\n\n\tname: loan_status\n\tdata_type: text\n\tdescription:  The status of the loan breakdown ('Not owing','Overage','Is owing').\n\n\tname: data_identification_verification\n\tdata_type: double precision\n\tdescription: The verification status for data identification in the loan breakdown.\n\n\tname: value_chain_management\n\tdata_type: double precision\n\tdescription: The value chain management status in the loan breakdown\n\n\tname: is_repaid\n\tdata_type: boolean\n\tdescription: A flag indic"], ["SH_trade_mart.dim_client", "Table Name: `trade_mart.dim_client`\nThis table is attributed to Workbench System. \nTable description: contains records on clients personal details like contact details for the on workbench platform, do not confuse the client in this table for the client on the comx_mart.dim_client\nIt contains the following columns:\n\tname: _airbyte_unique_key\n\tdata_type: text\n\tdescription: \n\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the client table\n\n\tname: bvn\n\tdata_type: text\n\tdescription: The Bank Verification Number associated with the client.\n\n\tname: cid\n\tdata_type: text\n\tdescription: The Client ID associated with the client.\n\n\tname: name\n\tdata_type: text\n\tdescription: The name of the client.\n\n\tname: email\n\tdata_type: text\n\tdescription: The email address of the client\n\n\tname: phone\n\tdata_type: text\n\tdescription: The phone number of the client.\n\n\tname: address\n\tdata_type: text\n\tdescription: The address of the client.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the client record was created.\n\n\tname: id_type\n\tdata_type: text\n\tdescription: The type of identification document used by the client e.g ('International Passport' ,'National ID Card','Cooperative ID Card', 'BVN')\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this client record.\n\n\tname: user_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the user associated with the client on account_user table.\n\n\tname: temp_cid\n\tdata_type: text\n\tdescription: Temporary Client ID associated with the client.\n\n\tname: contacted\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client has been contacted.\n\n\tname: id_number\n\tdata_type: text\n\tdescription: The identification number associated with the client.\n\n\tname: id_status\n\tdata_type: text\n\tdescription: The status of the client's identification document.\n\n\tname: is_active\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client is active.\n\n\tname: is_deleted\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client record is marked as deleted.\n\n\tname: language_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the language associated with the client.\n\n\tname: company_type\n\tdata_type: text\n\tdescription: The type of company associated with the client.\n\n\tname: country_code\n\tdata_type: text\n\tdescription: The country code associated with the client.\n\n\tname: matched_name\n\tdata_type: text\n\tdescription: The name associated with the client on the identification document provided.\n\n\tname: was_restored\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client record was restored.\n\n\tname: created_by_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the user who created the client record.\n\n\tname: date_restored\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the client record was restored.\n\n\tname: phone_invalid\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client's phone number is invalid.\n\n\tname: is_id_verified\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client's identification is verified.\n\n\tname: is_phone_verified\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client's phone number is verified.\n\n\tname: is_tenant_default\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client belongs to the default tenant.\n\n\tname: last_verify_attempt\n\tdata_type: timestamp w"], ["SH_trade_mart.dim_clientbank", "Table Name: `trade_mart.dim_clientbank`\nThis table is attributed to Workbench System. \nTable description: contains bank details of clients on workbench\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the client bank record.\n\n\tname: bvn\n\tdata_type: text\n\tdescription: The Bank Verification Number associated with the client bank account.\n\n\tname: account_number\n\tdata_type: text\n\tdescription: The account number of the client bank account.\n\n\tname: bank_code\n\tdata_type: text\n\tdescription: The code representing the bank associated with the client bank account.\n\n\tname: bank_name\n\tdata_type: text\n\tdescription: The name of the bank associated with the client bank account.\n\n\tname: bank_country\n\tdata_type: text\n\tdescription: The country where the bank associated with the client bank account is located.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the client bank record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this client bank record.\n\n\tname: client_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the client associated with the bank account ref workbench_mart.dim_client.id\n\n\tname: tenant_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the tenant  associated with the client bank account.\n\n\tname: preferred\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank account is the preferred account.\n\n\tname: is_deleted\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank record is marked as deleted.\n\n\tname: is_approved\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank account is approved.\n\n\tname: is_rejected\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank account is rejected.\n\n\tname: is_reverted\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank account status is reverted.\n\n\tname: is_verified\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank account is verified.\n\n\tname: is_bank_deleted\n\tdata_type: boolean\n\tdescription: A flag indicating whether the associated bank is deleted.\n\n\tname: is_nominated\n\tdata_type: boolean\n\tdescription: A flag indicating whether the client bank account is nominated.\n\n\tname: approval_date\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the client bank account was approved.\n\n\tname: approval_done\n\tdata_type: text\n\tdescription: Indicates the approval status of the client bank account.\n\n\tname: created_by_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the user who created the client bank record.\n\n\tname: next_approval\n\tdata_type: text\n\tdescription: The next approval status for the client bank account.\n\n\tname: rejected_date\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the client bank account was rejected\n\n\tname: revert_reason\n\tdata_type: text\n\tdescription: The reason for reverting the status of the client bank account.\n\n\tname: rejected_by_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the user who rejected the client bank account.\n\n\tname: created_offline\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the client bank record was created offline.\n\n\tname: rejection_reason\n\tdata_type: text\n\tdescription: The reason for rejecting the client bank account.\n\nHere are some releva"], ["SH_trade_mart.dim_farmer", "Table Name: `trade_mart.dim_farmer`\nThis table is attributed to Workbench System. \nTable description: contains records of farmer details that I/we have , note warehouse_name does not exist in this table but you can get it by joining on dim_warehouse\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the farmer.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the farmer record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this farmer record.\n\n\tname: is_deleted\n\tdata_type: boolean\n\tdescription: \n\n\tname: folio_id\n\tdata_type: text\n\tdescription: The folio ID associated with the farmer.\n\n\tname: title\n\tdata_type: text\n\tdescription: The title of the farmer.\n\n\tdata_type: text\n\tdescription: The first name of the farmer.\n\n\tdata_type: text\n\tdescription: The last name of the farmer.\n\n\tdata_type: text\n\tdescription: The middle of the farmer.\n\n\tname: address\n\tdata_type: text\n\tdescription: The address of the farmer.\n\n\tname: gender\n\tdata_type: text\n\tdescription: The gender of the farmer i.e (Male , Female)\n\n\tname: marital_status\n\tdata_type: text\n\tdescription: The marital status of the farmer.\n\n\tname: dob\n\tdata_type: date\n\tdescription: The date of birth of the farmer.\n\n\tname: phone\n\tdata_type: text\n\tdescription: The phone number of the farmer.\n\n\tname: village\n\tdata_type: text\n\tdescription: The village associated with the farmer.\n\n\tname: farm_size\n\tdata_type: text\n\tdescription: The size of the farm owned by the farmer.\n\n\tname: passport_type\n\tdata_type: text\n\tdescription: The type of passport held by the farmer('Driver's License','Others','University ID Card','Drivers License','International Passport','National ID Card','Cooperative ID Card','BVN','Voters Card')\n\n\tname: passport_number\n\tdata_type: text\n\tdescription: The passport number of the farmer.\n\n\tname: nok_phone\n\tdata_type: text\n\tdescription: The phone number of the next of kin (NOK) of the farmer.\n\n\tname: nok_name\n\tdata_type: text\n\tdescription: The name of the next of kin (NOK) of the farmer.\n\n\tname: nok_relationship\n\tdata_type: text\n\tdescription: The relationship of the next of kin (NOK) to the farmer.\n\n\tname: bvn\n\tdata_type: text\n\tdescription: The Bank Verification Number (BVN) of the farmer.\n\n\tname: farm_coordinates\n\tdata_type: text\n\tdescription: The coordinates of the farmer's farm.\n\n\tname: farm_coordinates_polygon\n\tdata_type: text\n\tdescription: The polygon coordinates of the farmer's farm.\n\n\tname: is_blacklist\n\tdata_type: boolean\n\tdescription: \n\n\tname: languages\n\tdata_type: text\n\tdescription: The languages spoken by the farmer.\n\n\tname: client_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the client associated with the farmer.\n\n\tname: warehouse_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the warehouse associated with the farmer. This can be used to join with the dim_warehouse table to get warehouse details ref dim_warehouse.id\n\n\tname: phone_invalid\n\tdata_type: boolean\n\tdescription: A flag indicating whether the phone number of the farmer is invalid.\n\n\tname: phone_number_status\n\tdata_type: text\n\tdescription: \n\n\tname: coordinate_status\n\tdata_type: text\n\tdescription: ('Pending Check','Invalid')\n\n\tname: id_status\n\tdata_type: text\n\tdescription: ('Pending Check','Confirmed Valid','Valid','Invalid','Confirmed Invalid').\n\n\tname: is_id_verified\n\tdata_type: boolean\n\tdescription: \n\n\tname: matched_name\n"], ["SH_trade_mart.dim_item", "Table Name: `trade_mart.dim_item`\nThis table is attributed to Workbench System. \nTable description: contains details of items which grn and loans are raised\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the item.\n\n\tname: tenant_id\n\tdata_type: bigint\n\tdescription: The identifier of the tenant associated with the item.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the item record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this item record.\n\n\tname: grade_one_deduction\n\tdata_type: double precision\n\tdescription: The deduction value for grade one of the item.\n\n\tname: grade_two_deduction\n\tdata_type: double precision\n\tdescription: The deduction value for grade two of the item.\n\n\tname: grade_three_deduction\n\tdata_type: double precision\n\tdescription: The deduction value for grade three of the item.\n\n\tname: name\n\tdata_type: text\n\tdescription: The name of the item.\n\n\tname: code\n\tdata_type: text\n\tdescription: The code representing the item\n\n\tname: product_type\n\tdata_type: text\n\tdescription: The type of the item, e.g., ('Discount Note' ,'Commodity','Fees','Input', 'Expense') \n\nHere are some relevant example rows (values in the same order as columns above)\n(2, 7, datetime.datetime(2021, 4, 5, 13, 21, 5, 351635, tzinfo=datetime.timezone.utc), datetime.datetime(2021, 4, 5, 13, 21, 5, 351680, tzinfo=datetime.timezone.utc), Decimal('0E-9'), Decimal('0E-9'), Decimal('0E-9'), '16Ltr Sprayer WACOT', 'WCT_SPY', 'Input')\n(3, 7, datetime.datetime(2021, 4, 5, 13, 21, 5, 358049, tzinfo=datetime.timezone.utc), datetime.datetime(2021, 4, 5, 13, 21, 5, 358091, tzinfo=datetime.timezone.utc), Decimal('0E-9'), Decimal('0E-9'), Decimal('0E-9'), 'Actara 25 WG 10x15x4 G NG0', 'CPP_ATA', 'Input')\n(4, 7, datetime.datetime(2021, 4, 5, 13, 21, 5, 363176, tzinfo=datetime.timezone.utc), datetime.datetime(2021, 4, 5, 13, 21, 5, 363225, tzinfo=datetime.timezone.utc), Decimal('0E-9'), Decimal('0E-9'), Decimal('0E-9'), 'AFEX Input Note', 'AFIN', 'Commodity')\n"], ["SH_trade_mart.fact_grn", "Table Name: `trade_mart.fact_grn`\nThis table is attributed to Workbench System. \nTable description: contains records on grn transactions\nIt contains the following columns:\n\tname: cid\n\tdata_type: text\n\tdescription: The unique identifier of the Goods Receipt Note (GRN).\n\n\tname: bags\n\tdata_type: bigint\n\tdescription: The number of bags in the GRN.\n\n\tname: grade\n\tdata_type: text\n\tdescription: The grade associated with the GRN (1,2,3)\n\n\tname: grn_id\n\tdata_type: text\n\tdescription: The unique identifier of the GRN.\n\n\tname: receipt_id\n\tdata_type: text\n\tdescription: The unique identifier of the receipt associated with the GRN.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the GRN was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this GRN.\n\n\tname: gross_weight\n\tdata_type: double precision\n\tdescription: The gross weight of the GRN.\n\n\tname: net_weight\n\tdata_type: double precision\n\tdescription: The net weight of the GRN.\n\n\tname: deduction\n\tdata_type: double precision\n\tdescription: The deduction amount in the GRN.\n\n\tname: total_deduction\n\tdata_type: double precision\n\tdescription: The total deduction amount in the GRN.\n\n\tname: moisture\n\tdata_type: double precision\n\tdescription: The moisture percentage in the GRN.\n\n\tname: total_commodity_price\n\tdata_type: double precision\n\tdescription: The total commodity price in the GRN.\n\n\tname: price_per_tonne\n\tdata_type: double precision\n\tdescription: The price per tonne in the GRN.\n\n\tname: transaction_type\n\tdata_type: text\n\tdescription: The type of transaction associated with the GRN('Broker Payment','Loan Repayment','Trade','Warehouse Receipt','Storage','Storage To Trade','Com To Input','Com For Equity')\n\n\tname: approval_permissions\n\tdata_type: text\n\tdescription: Permissions required for approval of the GRN.\n\n\tname: approval_done\n\tdata_type: text\n\tdescription: Approval status of the GRN.\n\n\tname: is_approved\n\tdata_type: boolean\n\tdescription: A flag indicating whether the GRN is approved.\n\n\tname: is_approval_completed\n\tdata_type: boolean\n\tdescription: A flag indicating whether the approval process for the GRN is completed.\n\n\tname: approval_date\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the GRN was approved.\n\n\tname: is_received_at_warehouse\n\tdata_type: boolean\n\tdescription: A flag indicating whether the GRN is received at the warehouse.\n\n\tname: is_reverted\n\tdata_type: boolean\n\tdescription: A flag indicating whether the GRN is reverted.\n\n\tname: rejection_reason\n\tdata_type: text\n\tdescription: The reason for rejection of the GRN.\n\n\tname: total_payable_price\n\tdata_type: double precision\n\tdescription: The total payable price in the GRN.\n\n\tname: transaction_fees\n\tdata_type: double precision\n\tdescription: The transaction fees associated with the GRN.\n\n\tname: is_processed\n\tdata_type: boolean\n\tdescription: A flag indicating whether the GRN is processed.\n\n\tname: is_disabled_for_listing\n\tdata_type: boolean\n\tdescription: A flag indicating whether the GRN is disabled for listing.\n\n\tname: spot_payment\n\tdata_type: double precision\n\tdescription: Spot payment amount in the GRN.\n\n\tname: employee_id\n\tdata_type: text\n\tdescription: The unique identifier of the employee associated with the GRN.\n\n\tname: cash_advance_account_pk\n\tdata_type: text\n\tdescription: The primary key of the cash advance account associated with the GRN.\n\n\tname: item_name\n\tdata_type: text\n\tdescription: The name of"], ["SH_trade_mart.dim_warehouse", "Table Name: `trade_mart.dim_warehouse`\nThis table is attributed to Workbench System. \nTable description: contains warehouse information like warehouse name and their location, note that to identify the location of a farmer you can use the warehouse that he belongs to\nIt contains the following columns:\n\tname: id\n\tdata_type: bigint\n\tdescription: The unique identifier of the warehouse.\n\n\tname: created\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating when the warehouse record was created.\n\n\tname: updated\n\tdata_type: timestamp with time zone\n\tdescription: Timestamp indicating the last update time for this warehouse record.\n\n\tname: location_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the location associated with the warehouse.\n\n\tname: tenant_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the tenant or customer associated with the warehouse.\n\n\tname: tenant_name\n\tdata_type: text\n\tdescription: The name of the tenant or customer associated with the warehouse.\n\n\tname: warehouse_name\n\tdata_type: text\n\tdescription: The name of the warehouse.\n\n\tname: warehouse_code\n\tdata_type: text\n\tdescription: The code representing the warehouse.\n\n\tname: capacity\n\tdata_type: bigint\n\tdescription:  The capacity or storage space of the warehouse.\n\n\tname: warehouse_manager_id\n\tdata_type: bigint\n\tdescription: The unique identifier of the warehouse manager.\n\n\tname: address\n\tdata_type: text\n\tdescription: The address of the warehouse.\n\n\tname: longitude\n\tdata_type: double precision\n\tdescription: The longitude coordinates of the warehouse location.\n\n\tname: latitude\n\tdata_type: double precision\n\tdescription: The latitude coordinates of the warehouse location.\n\n\tname: warehouse_email\n\tdata_type: text\n\tdescription: The email address of the warehouse\n\n\tname: location\n\tdata_type: text\n\tdescription: The specific location of the warehouse, subset of a state.\n\n\tname: state\n\tdata_type: text\n\tdescription: The state or province where the warehouse is located.\n\n\tname: capital\n\tdata_type: text\n\tdescription: The capital city or administrative center associated with the state or province.\n\n\tname: region_code\n\tdata_type: text\n\tdescription: The code representing the region where the warehouse is located.\n\n\tname: region_name\n\tdata_type: text\n\tdescription: The name of the region where the warehouse is located.\n\n\tname: country\n\tdata_type: text\n\tdescription: The country where the warehouse is located.\n\n\tname: country_capital\n\tdata_type: text\n\tdescription: The capital city or administrative center associated with the country.\n\n\tname: continent\n\tdata_type: text\n\tdescription: he continent where the warehouse is located.\n\n\tname: continent_subregion\n\tdata_type: text\n\tdescription: The subregion or subcontinent within the larger continent.\n\nHere are some relevant example rows (values in the same order as columns above)\n(101, datetime.datetime(2020, 8, 18, 16, 18, 39, 584043, tzinfo=datetime.timezone.utc), datetime.datetime(2022, 2, 22, 8, 46, 33, 835754, tzinfo=datetime.timezone.utc), 56, 7, 'AFEX Fair Trade Limited', 'ikom', '001-09202', 150, 6490, 'KM 2 Obudu Road Otere, Ikom Cross River State', None, None, '<EMAIL>', 'Cross River', 'Cross River', 'Calabar', 'SS', 'South South', 'Nigeria', 'Abuja', 'Africa', 'Western Africa')\n(295, datetime.datetime(2021, 11, 25, 13, 1, 39, 324073, tzinfo=datetime.timezone.utc), datetime.datetime(2021, 11, 29, 6, 38, 28, 475081, tzinfo=datetime.timezone.utc), 113, 84, 'AFTL Collateral Management', 'T"]]