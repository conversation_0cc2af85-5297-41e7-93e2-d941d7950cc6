author: "<PERSON>"
created: "2025-06-27"
version: "1.0.0"

rules:
  - rule_id: RX001
    name: "Completed Trade"
    applies_to: exchange_mart
    logic: "execution_id is not null"
    description: "Qualifies when a trade is completed"
    status: active

  - rule_id: RX002
    name: "Failed Trade"
    applies_to: exchange_mart
    logic: "execution_id is null"
    description: "Qualifies when a trade fails"
    status: active

  - rule_id: RX003
    name: "Matched Order"
    applies_to: exchange_mart
    logic: "matched_id is not null"
    description: "Qualifies when if a trade is matched"
    status: active

  - rule_id: RX004
    name: "cross trade"
    applies_to: exchange_mart
    logic: "oms_name is null"
    description: "Qualifies when a trade is a market cross on the exchange. It is cross because the system could not record the oms that matched the order"
    status: active
  
  - rule_id: RX005
    name: "OTC Trade"
    applies_to: exchange_mart
    logic: "security_type = 'OTC'"
    description: "Qualifies when a trade is an OTC trade"
    status: active
