#!/usr/bin/env python3
"""
Streamlit UI for Sherlock Agent - AFEX's AI Data Analyst
A user-friendly interface for interacting with the Sherlock Agent system.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import json
import uuid
from pathlib import Path
import time
from agent.plot_utils import auto_plot_from_dataframe
from agent.ui_components import (
    display_collapsible_sql_query,
    display_collapsible_data_results,
    create_enhanced_chat_message,
    create_agent_status_indicator,
    display_welcome_message,
    create_real_time_thinking_display,
    update_real_time_display
)
from agent.thinking_layer import get_process_tracker

# Configure Streamlit page
st.set_page_config(
    page_title="Sherlock Agent - AFEX AI Data Analyst",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }

    .sql-code {
        background-color: #f5f5f5;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #4caf50;
        font-family: 'Courier New', monospace;
    }

    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }

    /* Enhanced styling for collapsible sections */
    .streamlit-expanderHeader {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .streamlit-expanderContent {
        background-color: #ffffff;
        border-radius: 0 0 0.5rem 0.5rem;
        border: 1px solid #dee2e6;
        border-top: none;
    }

    /* Thinking process styling */
    .thinking-step {
        background: linear-gradient(90deg, #e3f2fd, #f3e5f5);
        border-left: 4px solid #2196f3;
        border-radius: 8px;
        padding: 12px;
        margin: 8px 0;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { opacity: 0.8; }
        50% { opacity: 1.0; }
        100% { opacity: 0.8; }
    }

    /* Data table styling */
    .dataframe {
        border-radius: 0.5rem;
        overflow: hidden;
        border: 1px solid #dee2e6;
    }

    /* Chart container styling */
    .chart-container {
        background-color: #ffffff;
        border-radius: 0.5rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'sessions' not in st.session_state:
        st.session_state.sessions = {}
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = None
    if 'agent' not in st.session_state:
        st.session_state.agent = None
    if 'agent_initialized' not in st.session_state:
        st.session_state.agent_initialized = False

def create_new_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())[:8]
    session_name = f"Session {len(st.session_state.sessions) + 1}"
    
    st.session_state.sessions[session_id] = {
        'name': session_name,
        'created_at': datetime.now(),
        'messages': [],
        'queries_executed': 0,
        'total_rows_returned': 0
    }
    st.session_state.current_session_id = session_id
    return session_id

def initialize_agent():
    """Initialize the Sherlock Agent"""
    if not st.session_state.agent_initialized:
        try:
            with st.spinner("🚀 Initializing Sherlock Agent..."):
                from agent.workflow import Agent
                from agent.logger import get_logger
                
                logger = get_logger("streamlit_app")
                logger.info("Initializing Sherlock Agent from Streamlit UI")
                
                st.session_state.agent = Agent()
                st.session_state.agent.build_workflow()
                st.session_state.agent_initialized = True
                
                logger.info("Sherlock Agent successfully initialized in Streamlit UI")
                return True
        except Exception as e:
            st.error(f"❌ Failed to initialize agent: {str(e)}")
            return False
    return True

def sidebar():
    """Create the sidebar with session management"""
    st.sidebar.markdown("## 🔍 Sherlock Agent")
    st.sidebar.markdown("*AFEX's AI Data Analyst*")
    st.sidebar.divider()
    
    # Session Management
    st.sidebar.markdown("### 📝 Sessions")
    
    # Create new session button
    if st.sidebar.button("➕ New Session", use_container_width=True):
        create_new_session()
        st.rerun()
    
    # Session selector
    if st.session_state.sessions:
        session_options = {
            session_id: f"{data['name']} ({data['created_at'].strftime('%H:%M')})"
            for session_id, data in st.session_state.sessions.items()
        }
        
        selected_session = st.sidebar.selectbox(
            "Select Session:",
            options=list(session_options.keys()),
            format_func=lambda x: session_options[x],
            index=list(session_options.keys()).index(st.session_state.current_session_id) 
                  if st.session_state.current_session_id in session_options else 0
        )
        
        if selected_session != st.session_state.current_session_id:
            st.session_state.current_session_id = selected_session
            st.rerun()
        
        # Session stats and controls
        if st.session_state.current_session_id:
            current_session = st.session_state.sessions[st.session_state.current_session_id]
            st.sidebar.markdown("#### 📊 Session Stats")
            st.sidebar.metric("Messages", len(current_session['messages']))
            st.sidebar.metric("Queries Executed", current_session['queries_executed'])
            st.sidebar.metric("Total Rows", current_session['total_rows_returned'])

            # Clear session button in sidebar
            st.sidebar.markdown("#### 🔧 Session Controls")
            if st.sidebar.button("🗑️ Clear Session", use_container_width=True):
                current_session['messages'] = []
                current_session['queries_executed'] = 0
                current_session['total_rows_returned'] = 0
                st.rerun()
    
    st.sidebar.divider()
    
    # Agent Status with thinking process
    st.sidebar.markdown("### 🤖 Agent Status")

    # Get current thinking process if any
    current_thinking_process_id = None
    if st.session_state.current_session_id:
        tracker = get_process_tracker()
        process = tracker.get_process_by_session(st.session_state.current_session_id)
        if process and process.status == "active":
            current_thinking_process_id = process.process_id

    if st.session_state.agent_initialized:
        if current_thinking_process_id:
            tracker = get_process_tracker()
            process = tracker.get_process(current_thinking_process_id)
            current_step = process.current_step if process else None
            if current_step:
                st.sidebar.info(f"🔄 {current_step.description}")
            else:
                st.sidebar.success("✅ Agent Ready")
        else:
            st.sidebar.success("✅ Agent Ready")
    else:
        st.sidebar.warning("⚠️ Agent Not Initialized")
        if st.sidebar.button("🔄 Initialize Agent"):
            initialize_agent()
            st.rerun()
    
    # System Info
    st.sidebar.divider()
    st.sidebar.markdown("### ℹ️ System Info")
    st.sidebar.info("""
    **Sherlock Agent** helps you analyze AFEX's data by:
    - Understanding business questions
    - Writing optimized SQL queries
    - Executing queries safely
    - Providing actionable insights
    """)

def display_chat_message(message_type, content, timestamp=None, sql_query=None, data=None, thinking_process_id=None):
    """Display a chat message using Streamlit's native chat components with collapsible sections"""
    if timestamp is None:
        timestamp = datetime.now()

    # Use enhanced chat message component
    create_enhanced_chat_message(
        message_type=message_type,
        content=content,
        timestamp=timestamp,
        sql_query=sql_query,
        data=data,
        thinking_process_id=thinking_process_id
    )

# These functions are now replaced by the enhanced UI components
# display_sql_query and display_data_results are handled in create_enhanced_chat_message

def main_chat_interface():
    """Main chat interface"""
    st.markdown('<h1 class="main-header">🔍 Sherlock Agent - AFEX AI Data Analyst</h1>', 
                unsafe_allow_html=True)
    
    # Check if agent is initialized
    if not st.session_state.agent_initialized:
        st.warning("⚠️ Agent not initialized. Please initialize the agent from the sidebar.")
        return
    
    # Check if session exists
    if not st.session_state.current_session_id:
        display_welcome_message()
        return
    
    current_session = st.session_state.sessions[st.session_state.current_session_id]
    
    # Display live thinking process if active
    tracker = get_process_tracker()
    active_process = tracker.get_process_by_session(st.session_state.current_session_id)
    if active_process and active_process.status == "active":
        with st.expander("🧠 Live Agent Thinking", expanded=True):
            from agent.ui_components import render_current_step_display
            step_placeholder = st.empty()
            render_current_step_display(active_process.process_id, step_placeholder)

    # Display chat history
    if current_session['messages']:
        st.markdown("### 💬 Conversation")

        # Chat container with better organization
        chat_container = st.container()
        with chat_container:
            for message in current_session['messages']:
                display_chat_message(
                    message_type=message['type'],
                    content=message['content'],
                    timestamp=message['timestamp'],
                    sql_query=message.get('sql_query'),
                    data=message.get('data'),
                    thinking_process_id=message.get('thinking_process_id')
                )
    else:
        st.markdown("### 💬 Start a Conversation")
        st.info("Ask Sherlock a question using the chat input below!")
    
    # Chat input using streamlit's chat_input
    user_input = st.chat_input(
        placeholder="Ask Sherlock about AFEX's data... e.g., What is the total number of farmers?"
    )

    # Process user input
    if user_input and user_input.strip():
        # Add user message
        user_message = {
            'type': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        }
        current_session['messages'].append(user_message)
        
        # Get agent response
        with st.spinner("� Sherlock is thinking..."):
            try:
                response = st.session_state.agent.chat(user_input, st.session_state.current_session_id)
                
                # Add agent response
                agent_message = {
                    'type': 'agent',
                    'content': response['response'],
                    'timestamp': datetime.now(),
                    'sql_query': response.get('sql_query'),
                    'data': response.get('data'),
                    'thinking_process_id': response.get('thinking_process_id')
                }
                current_session['messages'].append(agent_message)
                
                # Update session stats
                current_session['queries_executed'] += 1
                if response.get('data') and isinstance(response['data'], list):
                    current_session['total_rows_returned'] += len(response['data'])
                
            except Exception as e:
                st.error(f"❌ Error: {str(e)}")
                agent_message = {
                    'type': 'agent',
                    'content': f"I apologize, but I encountered an error: {str(e)}",
                    'timestamp': datetime.now()
                }
                current_session['messages'].append(agent_message)
        
        st.rerun()

def main():
    """Main application function"""
    initialize_session_state()
    
    # Initialize agent on startup
    if not st.session_state.agent_initialized:
        initialize_agent()
    
    # Create sidebar
    sidebar()
    
    # Main interface
    main_chat_interface()

if __name__ == "__main__":
    main()
