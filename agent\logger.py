"""
Centralized logging configuration for the Sherlock Agent package.
Provides structured logging with different levels and formatters.
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
import json


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
            
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # Format the message
        formatted = super().format(record)
        return f"{color}{formatted}{reset}"


class AgentLogger:
    """Centralized logger configuration for the agent package"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.setup_logging()
            self._initialized = True
    
    def setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # Clear any existing handlers
        root_logger.handlers.clear()
        
        # Console handler with colored output
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter(
            '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler for all logs (JSON format)
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / "agent.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = JSONFormatter()
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Error file handler (only errors and critical)
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / "agent_errors.log",
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
        
        # Database operations log
        db_handler = logging.handlers.RotatingFileHandler(
            log_dir / "database.log",
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3
        )
        db_handler.setLevel(logging.DEBUG)
        db_handler.setFormatter(file_formatter)
        
        # Add filter to only log database-related messages
        db_handler.addFilter(lambda record: 'database' in record.name.lower() or 'db' in record.name.lower())
        root_logger.addHandler(db_handler)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    # Ensure logging is initialized
    AgentLogger()
    
    logger = logging.getLogger(name)
    return logger


def log_function_call(func_name: str, **kwargs):
    """
    Log function call with parameters.
    
    Args:
        func_name: Name of the function being called
        **kwargs: Function parameters to log
    """
    logger = get_logger('agent.function_calls')
    logger.debug(f"Calling {func_name}", extra={'extra_fields': {'function': func_name, 'parameters': kwargs}})


def log_database_operation(operation: str, query: Optional[str] = None, success: bool = True, error: Optional[str] = None, **kwargs):
    """
    Log database operations.
    
    Args:
        operation: Type of operation (connect, query, disconnect, etc.)
        query: SQL query if applicable
        success: Whether operation was successful
        error: Error message if operation failed
        **kwargs: Additional context
    """
    logger = get_logger('agent.database')
    
    extra_fields = {
        'operation': operation,
        'success': success,
        **kwargs
    }
    
    if query:
        extra_fields['query'] = query
    
    if success:
        logger.info(f"Database operation successful: {operation}", extra={'extra_fields': extra_fields})
    else:
        extra_fields['error'] = error
        logger.error(f"Database operation failed: {operation}", extra={'extra_fields': extra_fields})


def log_agent_step(step: str, state: Optional[dict] = None, **kwargs):
    """
    Log agent workflow steps.
    
    Args:
        step: Name of the step
        state: Current agent state (will be summarized)
        **kwargs: Additional context
    """
    logger = get_logger('agent.workflow')
    
    extra_fields = {
        'step': step,
        **kwargs
    }
    
    if state:
        # Summarize state to avoid logging sensitive data
        state_summary = {
            'message_count': len(state.get('messages', [])),
            'has_sql_query': bool(state.get('sql_query')),
            'has_result': bool(state.get('result')),
            'command': str(state.get('command', 'unknown'))
        }
        extra_fields['state_summary'] = state_summary
    
    logger.info(f"Agent step: {step}", extra={'extra_fields': extra_fields})


def log_error(error: Exception, context: str = "", **kwargs):
    """
    Log errors with context.
    
    Args:
        error: Exception that occurred
        context: Context where error occurred
        **kwargs: Additional context
    """
    logger = get_logger('agent.errors')
    
    extra_fields = {
        'error_type': type(error).__name__,
        'context': context,
        **kwargs
    }
    
    logger.error(f"Error in {context}: {str(error)}", exc_info=error, extra={'extra_fields': extra_fields})


# Initialize logging when module is imported
AgentLogger()
