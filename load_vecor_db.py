from langchain_qdrant import QdrantVectorStore
from langchain.embeddings import OpenAIEmbeddings
from langchain_core.documents import Document
from qdrant_client import QdrantClient

EMBEDDINGS = OpenAIEmbeddings()
QDRANT_URL = "http://localhost:6333"  # or your Qdrant Cloud endpoint
API_KEY = None  # if needed

def connect_or_create(collection_name: str, documents: list):
    """
    Connects to a Qdrant collection (creates it if missing),
    and adds provided documents (list of langchain Document objects).
    Returns the QdrantVectorStore instance.
    """
    client = QdrantClient(url=QDRANT_URL, api_key=API_KEY)
    vectorstore = QdrantVectorStore.from_documents(
        documents, EMBEDDINGS,
        url=QDRANT_URL, api_key=API_KEY,
        collection_name=collection_name,
        force_recreate=False  # reuse existing collection
    )
    return vectorstore


def add_single(doc: Document, collection_name: str):
    """
    Connects to an existing Qdrant collection and adds a single Document.
    """
    vs = QdrantVectorStore.from_existing_collection(
        embeddings=EMBEDDINGS,
        collection_name=collection_name,
        url=QDRANT_URL, api_key=API_KEY,
    )
    vs.add_documents([doc])
    return vs
