import os
from psycopg2 import connect
from sqlalchemy.exc import (
    NoSuchTableError, NoSuchColumnError,
    ProgrammingError, DataError,
    InterfaceError, OperationalError,
    DatabaseError
)
from agent.config import settings
from agent.logger import get_logger, log_database_operation, log_error
from contextlib import contextmanager
from sqlalchemy import create_engine, text
import time

from pathlib import Path
import sqlite3

# Get logger for this module
logger = get_logger(__name__)

def get_sqlite_conn(path=None, check_same_thread=False):
    """Get SQLite connection with logging"""
    try:
        if path is None:
            try:
                root_path = Path(__file__).parent.parent
            except Exception as e:
                root_path = Path(os.getcwd())
            path = root_path / "memory" / "agent.db"
            path.parent.mkdir(parents=True, exist_ok=True)

        logger.debug(f"Connecting to SQLite database: {path}")
        conn = sqlite3.connect(path, check_same_thread=check_same_thread)
        log_database_operation("sqlite_connect", success=True, database_path=str(path))
        return conn
    except Exception as e:
        log_error(e, "get_sqlite_conn", database_path=str(path) if path else "unknown")
        raise




def connect_db():
    """Connect to PostgreSQL database with logging"""
    start_time = time.time()
    try:
        logger.info("Attempting to connect to PostgreSQL database")
        connection_string = f"postgresql://{settings.POSTGRES_USERNAME}:***@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}"
        logger.debug(f"Connection string: {connection_string}")

        engine = create_engine(f"postgresql://{settings.POSTGRES_USERNAME}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}")
        conn = engine.connect()

        connection_time = time.time() - start_time
        logger.info(f"✅ Successfully connected to PostgreSQL database in {connection_time:.2f}s")
        log_database_operation(
            "postgres_connect",
            success=True,
            host=str(settings.POSTGRES_HOST),
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DATABASE,
            connection_time=connection_time
        )
        return conn
    except Exception as e:
        connection_time = time.time() - start_time
        logger.error(f"❌ Failed to connect to PostgreSQL database after {connection_time:.2f}s")
        log_database_operation(
            "postgres_connect",
            success=False,
            error=str(e),
            host=str(settings.POSTGRES_HOST),
            port=settings.POSTGRES_PORT,
            database=settings.POSTGRES_DATABASE,
            connection_time=connection_time
        )
        log_error(e, "connect_db")
        raise

@contextmanager
def open_db_connection():
    """Context manager for database connections with logging"""
    conn = None
    try:
        logger.debug("Opening database connection context")
        conn = connect_db()
        yield conn
    except Exception as e:
        logger.error("Error in database connection context")
        log_error(e, "open_db_connection")
        raise
    finally:
        if conn:
            try:
                conn.close()
                logger.debug("Database connection closed successfully")
                log_database_operation("postgres_disconnect", success=True)
            except Exception as e:
                logger.warning(f"Error closing database connection: {e}")
                log_database_operation("postgres_disconnect", success=False, error=str(e))


def get_engine():
    """Get SQLAlchemy engine with logging"""
    try:
        logger.debug("Creating SQLAlchemy engine")
        engine = create_engine(f"postgresql://{settings.POSTGRES_USERNAME}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}")
        log_database_operation("engine_create", success=True)
        return engine
    except Exception as e:
        log_error(e, "get_engine")
        log_database_operation("engine_create", success=False, error=str(e))
        raise

def validate_sql_query(query: str) -> dict:
    """
    Validates a SQL query for syntax and reference correctness using EXPLAIN.

    Args:
        query: SQL query to validate

    Returns:
        dict with keys:
            - 'valid' (bool): True if query is valid
            - 'error' (str or None): Error message if invalid
            - 'type' (str or None): Type of error ('query', 'connection', etc.)
    """
    start_time = time.time()
    logger.debug(f"Validating SQL query: {query[:100]}...")

    try:
        engine = get_engine()
        with engine.connect() as conn:
            try:
                conn.execute(text(f"EXPLAIN {query}"))
                validation_time = time.time() - start_time
                logger.debug(f"✅ SQL query validation successful in {validation_time:.3f}s")
                log_database_operation(
                    "sql_validate",
                    query=query[:200] + "..." if len(query) > 200 else query,
                    success=True,
                    validation_time=validation_time
                )
                return {'valid': True, 'error': None, 'type': None}

            except Exception as e:
                validation_time = time.time() - start_time
                error_type = 'unknown'

                if isinstance(e, (NoSuchTableError, NoSuchColumnError, ProgrammingError, DataError)):
                    error_type = 'query'
                    logger.debug(f"❌ SQL query validation failed (query error) in {validation_time:.3f}s: {str(e)}")
                elif isinstance(e, InterfaceError):
                    error_type = 'connection'
                    logger.warning(f"❌ SQL query validation failed (connection error) in {validation_time:.3f}s: {str(e)}")
                elif isinstance(e, (OperationalError, DatabaseError)):
                    error_type = 'connection'
                    logger.error(f"❌ SQL query validation failed (database error) in {validation_time:.3f}s: {str(e)}")
                else:
                    error_type = 'unknown'
                    logger.error(f"❌ SQL query validation failed (unknown error) in {validation_time:.3f}s: {str(e)}")

                log_database_operation(
                    "sql_validate",
                    query=query[:200] + "..." if len(query) > 200 else query,
                    success=False,
                    error=str(e),
                    error_type=error_type,
                    validation_time=validation_time
                )

                return {'valid': False, 'error': str(e).strip(), 'type': error_type}

    except Exception as e:
        validation_time = time.time() - start_time
        logger.error(f"❌ SQL query validation failed (engine error) in {validation_time:.3f}s: {str(e)}")
        log_error(e, "validate_sql_query", query=query[:100])
        return {'valid': False, 'error': str(e).strip(), 'type': 'engine'}

