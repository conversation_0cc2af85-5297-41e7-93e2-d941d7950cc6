from enum import Enum
from pydantic import BaseModel, Field, field_validator
from typing import Annotated, TypedDict, Optional, Dict, Any, List
from langgraph.graph.message import add_messages
from datetime import datetime
import uuid
# Removed direct import of validate_sql_query to break circular dependency

class UserQuery(BaseModel):
    query: str = Field(..., description="The user's query")

class VectorSearchQuery(UserQuery):
    query_str: str = Field(..., description="The query string to search for")
    top_k: int = 3
    metadata: dict = {}

class SQLQuery(BaseModel):
    sql_query: str = Field(..., description="The SQL query to run")
    
    @field_validator("sql_query")
    @classmethod
    def validate_sql(cls, v):
        if not v.startswith("SELECT"):
            raise ValueError("SQL query must start with SELECT")
        if "FROM" not in v:
            raise ValueError("SQL query must contain FROM")

        # Lazy import to avoid circular dependency
        from agent.db import validate_sql_query
        validate = validate_sql_query(v)
        if not validate['valid']:
            if validate['type']in ('connection', 'unknown'):
                raise Exception(f"Failed to validate Query: {validate['error']}")
            raise ValueError(validate['error'])

        return v


class Control(Enum):
    AGENT = "AGENT"
    USER = "USER"
    TOOL = "TOOL"

class SQLResult(BaseModel):
    result: dict = Field(..., description="The result of the SQL query")
    sql_query: str = Field(..., description="The SQL query that was run")

class FailedSQLQuery(BaseModel):
    sql_query: str 
    error: str

class SQLWriteQuery(BaseModel):
    user_query: str = Field(..., description="The user's query")
    schema_info: str = Field(..., description="The schema information provided to you")

class AgentState(TypedDict):
    messages: Annotated[list, add_messages]
    sql_query: str
    result: dict
    query_plan: str
    schema_info: list
    command: Control = Control.AGENT
    thinking_process_id: Optional[str] = None


# Thinking Layer Models
class ThinkingStepType(Enum):
    """Types of thinking steps the agent can perform"""
    QUERY_ANALYSIS = "query_analysis"
    SCHEMA_RETRIEVAL = "schema_retrieval"
    SQL_GENERATION = "sql_generation"
    QUERY_VALIDATION = "query_validation"
    QUERY_EXECUTION = "query_execution"
    RESULT_ANALYSIS = "result_analysis"
    RESPONSE_GENERATION = "response_generation"


class ThinkingStepStatus(Enum):
    """Status of a thinking step"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ThinkingStep(BaseModel):
    """Represents a single step in the agent's thinking process"""
    step_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    step_type: ThinkingStepType
    status: ThinkingStepStatus = ThinkingStepStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    description: str = ""
    details: Dict[str, Any] = Field(default_factory=dict)
    error_message: Optional[str] = None

    @property
    def duration(self) -> Optional[float]:
        """Calculate step duration in seconds"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None

    @property
    def is_active(self) -> bool:
        """Check if step is currently active"""
        return self.status == ThinkingStepStatus.IN_PROGRESS


class ThinkingProcess(BaseModel):
    """Represents the complete thinking process for a user query"""
    process_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str
    user_query: str
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    status: str = "active"  # active, completed, failed
    steps: List[ThinkingStep] = Field(default_factory=list)
    current_step_index: int = 0

    @property
    def current_step(self) -> Optional[ThinkingStep]:
        """Get the currently active step"""
        for step in self.steps:
            if step.is_active:
                return step
        return None

    @property
    def completed_steps(self) -> List[ThinkingStep]:
        """Get all completed steps"""
        return [step for step in self.steps if step.status == ThinkingStepStatus.COMPLETED]

    @property
    def progress_percentage(self) -> float:
        """Calculate overall progress percentage"""
        if not self.steps:
            return 0.0
        completed = len(self.completed_steps)
        return (completed / len(self.steps)) * 100

    def get_step_by_type(self, step_type: ThinkingStepType) -> Optional[ThinkingStep]:
        """Get a step by its type"""
        for step in self.steps:
            if step.step_type == step_type:
                return step
        return None