from enum import Enum
from pydantic import BaseModel, Field, field_validator
from typing import Annotated, TypedDict
from langgraph.graph.message import add_messages
# Removed direct import of validate_sql_query to break circular dependency

class UserQuery(BaseModel):
    query: str = Field(..., description="The user's query")

class VectorSearchQuery(UserQuery):
    query_str: str = Field(..., description="The query string to search for")
    top_k: int = 3
    metadata: dict = {}

class SQLQuery(BaseModel):
    sql_query: str = Field(..., description="The SQL query to run")
    
    @field_validator("sql_query")
    @classmethod
    def validate_sql(cls, v):
        if not v.startswith("SELECT"):
            raise ValueError("SQL query must start with SELECT")
        if "FROM" not in v:
            raise ValueError("SQL query must contain FROM")

        # Lazy import to avoid circular dependency
        from agent.db import validate_sql_query
        validate = validate_sql_query(v)
        if not validate['valid']:
            if validate['type']in ('connection', 'unknown'):
                raise Exception(f"Failed to validate Query: {validate['error']}")
            raise ValueError(validate['error'])

        return v


class Control(Enum):
    AGENT = "AGENT"
    USER = "USER"
    TOOL = "TOOL"

class SQLResult(BaseModel):
    result: dict = Field(..., description="The result of the SQL query")
    sql_query: str = Field(..., description="The SQL query that was run")

class FailedSQLQuery(BaseModel):
    sql_query: str 
    error: str

class SQLWriteQuery(BaseModel):
    user_query: str = Field(..., description="The user's query")
    schema_info: str = Field(..., description="The schema information provided to you")

class AgentState(TypedDict):
    messages: Annotated[list, add_messages]
    sql_query: str
    result: dict
    query_plan: str
    schema_info: list
    command: Control = Control.AGENT