# Sherlock Agent Logging System

## Overview

The Sherlock Agent now includes a comprehensive logging system that tracks all errors and important operational steps, including database connections, SQL query execution, and agent workflow steps.

## Features

### 🎯 **Structured Logging**
- **JSON Format**: All logs are stored in structured JSON format for easy parsing and analysis
- **Multiple Log Levels**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Contextual Information**: Each log entry includes timestamp, module, function, and line number

### 📊 **Database Operation Logging**
- **Connection Tracking**: Logs all database connection attempts with timing
- **Query Execution**: Tracks SQL query execution with performance metrics
- **Error Handling**: Detailed error logging for database failures
- **Validation Logging**: SQL query validation results

### 🤖 **Agent Workflow Logging**
- **Step Tracking**: Logs each step in the agent workflow
- **State Monitoring**: Tracks agent state changes (without sensitive data)
- **Performance Metrics**: Execution times for various operations
- **Error Context**: Detailed error information with context

### 📁 **Log File Organization**
- `logs/agent.log` - All application logs (rotating, 10MB max)
- `logs/agent_errors.log` - Error and critical logs only (rotating, 5MB max)
- `logs/database.log` - Database-specific operations (rotating, 5MB max)

## Usage

### Basic Logging

```python
from agent.logger import get_logger

logger = get_logger(__name__)
logger.info("This is an info message")
logger.error("This is an error message")
```

### Database Operation Logging

```python
from agent.logger import log_database_operation

# Log successful operation
log_database_operation(
    "sql_execute",
    query="SELECT * FROM users",
    success=True,
    execution_time=0.123,
    row_count=50
)

# Log failed operation
log_database_operation(
    "sql_execute",
    query="SELECT * FROM invalid_table",
    success=False,
    error="Table 'invalid_table' doesn't exist"
)
```

### Agent Step Logging

```python
from agent.logger import log_agent_step

log_agent_step(
    "query_executor",
    state=current_state,
    additional_context="Processing user query"
)
```

### Error Logging

```python
from agent.logger import log_error

try:
    # Some operation
    pass
except Exception as e:
    log_error(e, "function_name", additional_context="value")
```

## Log Entry Structure

### Standard Log Entry
```json
{
    "timestamp": "2024-01-15T10:30:45.123Z",
    "level": "INFO",
    "logger": "agent.db",
    "message": "Database connection successful",
    "module": "db",
    "function": "connect_db",
    "line": 45
}
```

### Database Operation Log Entry
```json
{
    "timestamp": "2024-01-15T10:30:45.123Z",
    "level": "INFO",
    "logger": "agent.database",
    "message": "Database operation successful: sql_execute",
    "module": "tools",
    "function": "execute_sql_query",
    "line": 67,
    "extra_fields": {
        "operation": "sql_execute",
        "success": true,
        "query": "SELECT * FROM users LIMIT 10",
        "execution_time": 0.123,
        "row_count": 10
    }
}
```

### Agent Step Log Entry
```json
{
    "timestamp": "2024-01-15T10:30:45.123Z",
    "level": "INFO",
    "logger": "agent.workflow",
    "message": "Agent step: query_executor",
    "module": "nodes",
    "function": "QueryExecutor",
    "line": 185,
    "extra_fields": {
        "step": "query_executor",
        "state_summary": {
            "message_count": 3,
            "has_sql_query": true,
            "has_result": false,
            "command": "AGENT"
        }
    }
}
```

## Key Logged Events

### 🔌 **Database Events**
- ✅ **Connection Success**: PostgreSQL and SQLite connections
- ❌ **Connection Failures**: With detailed error messages
- 📊 **Query Execution**: SQL queries with performance metrics
- 🔍 **Query Validation**: SQL syntax and reference validation
- 🔄 **Connection Lifecycle**: Open/close operations

### 🤖 **Agent Events**
- 🚀 **Agent Initialization**: Startup and configuration
- 💭 **Agent Thoughts**: LLM responses and reasoning
- 🔧 **Tool Usage**: Schema retrieval and other tools
- ⚡ **Query Execution**: SQL query processing
- 💬 **User Interactions**: Chat messages and responses

### ⚠️ **Error Events**
- 🚨 **Database Errors**: Connection, query, and validation failures
- 🔥 **Agent Errors**: Workflow and processing errors
- 🛠️ **Tool Errors**: Schema retrieval and execution failures
- 📝 **Validation Errors**: Input and query validation issues

## Console Output

The logging system provides colored console output for better readability:
- 🟢 **INFO**: Green text for successful operations
- 🟡 **WARNING**: Yellow text for warnings
- 🔴 **ERROR**: Red text for errors
- 🔵 **DEBUG**: Cyan text for debug information

## Performance Monitoring

The logging system tracks:
- **Database Connection Times**: How long connections take to establish
- **Query Execution Times**: SQL query performance
- **Agent Response Times**: End-to-end processing times
- **Validation Times**: SQL validation performance

## Testing

Run the logging test suite:

```bash
python test_logging.py
```

This will:
1. Test all logging features
2. Test database connections with logging
3. Test agent initialization with logging
4. Create sample log files for inspection

## Configuration

The logging system is automatically configured when any agent module is imported. No additional setup is required.

### Log Rotation
- **agent.log**: Rotates at 10MB, keeps 5 backups
- **agent_errors.log**: Rotates at 5MB, keeps 3 backups  
- **database.log**: Rotates at 5MB, keeps 3 backups

### Log Levels
- **Production**: INFO and above
- **Development**: DEBUG and above (can be configured)

## Benefits

1. **🔍 Debugging**: Detailed error information with context
2. **📊 Monitoring**: Performance metrics and operational insights
3. **🔒 Security**: Database connection and query tracking
4. **📈 Analytics**: Usage patterns and performance trends
5. **🚨 Alerting**: Error detection and notification capabilities

## Example Log Output

```
2024-01-15 10:30:45 | INFO     | agent.workflow | 🚀 Initializing Sherlock Agent
2024-01-15 10:30:45 | INFO     | agent.db       | 📊 Connecting to PostgreSQL database...
2024-01-15 10:30:45 | INFO     | agent.db       | ✅ Successfully connected to PostgreSQL database in 0.12s
2024-01-15 10:30:46 | INFO     | agent.workflow | 💬 User message: Show me all users
2024-01-15 10:30:46 | INFO     | agent.nodes    | 🤖 Agent node activated
2024-01-15 10:30:47 | INFO     | agent.tools    | 🔍 Executing SQL query: SELECT * FROM users...
2024-01-15 10:30:47 | INFO     | agent.tools    | ✅ SQL query executed successfully in 0.08s, returned 25 rows
```

This comprehensive logging system provides full visibility into the Sherlock Agent's operations, making debugging, monitoring, and optimization much easier.
