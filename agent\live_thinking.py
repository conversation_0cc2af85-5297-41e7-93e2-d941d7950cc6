#!/usr/bin/env python3
"""
Live Thinking Display Components

This module provides Streamlit components for displaying real-time agent thinking
with collapsible sections and live updates.
"""

import streamlit as st
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
import time

from agent.thinking_layer import get_process_tracker
from agent.models import ThinkingProcess, ThinkingStep, ThinkingStepType, ThinkingStepStatus
from agent.logger import get_logger

logger = get_logger(__name__)


def get_step_emoji(step_type: ThinkingStepType) -> str:
    """Get emoji for each step type"""
    emoji_map = {
        ThinkingStepType.QUERY_ANALYSIS: "🔍",
        ThinkingStepType.SCHEMA_RETRIEVAL: "📊",
        ThinkingStepType.SQL_GENERATION: "⚡",
        ThinkingStepType.QUERY_VALIDATION: "✅",
        ThinkingStepType.QUERY_EXECUTION: "🚀",
        ThinkingStepType.RESULT_ANALYSIS: "📈",
        ThinkingStepType.RESPONSE_GENERATION: "💬"
    }
    return emoji_map.get(step_type, "🔧")


def get_status_emoji(status: ThinkingStepStatus) -> str:
    """Get emoji for step status"""
    status_map = {
        ThinkingStepStatus.NOT_STARTED: "⏳",
        ThinkingStepStatus.IN_PROGRESS: "🔄",
        ThinkingStepStatus.COMPLETED: "✅",
        ThinkingStepStatus.FAILED: "❌"
    }
    return status_map.get(status, "❓")


def format_duration(duration: Optional[float]) -> str:
    """Format duration in a human-readable way"""
    if duration is None:
        return "—"
    
    if duration < 1:
        return f"{duration*1000:.0f}ms"
    elif duration < 60:
        return f"{duration:.1f}s"
    else:
        minutes = int(duration // 60)
        seconds = duration % 60
        return f"{minutes}m {seconds:.1f}s"


def show_thinking_status(process_id: str, style: str = "detailed") -> None:
    """
    Display the current thinking status
    
    Args:
        process_id: ID of the thinking process
        style: Display style - "simple", "current_step", or "detailed"
    """
    tracker = get_process_tracker()
    process = tracker.get_process(process_id)
    
    if not process:
        st.warning("No active thinking process found")
        return
    
    if style == "simple":
        _show_simple_status(process)
    elif style == "current_step":
        _show_current_step(process)
    else:
        _show_detailed_status(process)


def _show_simple_status(process: ThinkingProcess) -> None:
    """Show simple status with just current step"""
    current_step = process.current_step
    
    if current_step:
        step_emoji = get_step_emoji(current_step.step_type)
        st.info(f"{step_emoji} {current_step.description}")
    elif process.status == "completed":
        st.success("✅ Thinking process completed")
    elif process.status == "failed":
        st.error("❌ Thinking process failed")
    else:
        st.info("🤔 Preparing to think...")


def _show_current_step(process: ThinkingProcess) -> None:
    """Show current step with progress"""
    col1, col2 = st.columns([3, 1])
    
    with col1:
        current_step = process.current_step
        if current_step:
            step_emoji = get_step_emoji(current_step.step_type)
            st.markdown(f"**{step_emoji} {current_step.description}**")
        elif process.status == "completed":
            st.success("✅ All steps completed")
        else:
            st.info("🤔 Initializing...")
    
    with col2:
        progress = process.progress_percentage
        st.metric("Progress", f"{progress:.0f}%")


def _show_detailed_status(process: ThinkingProcess) -> None:
    """Show detailed status with all steps"""
    # Progress bar
    progress = process.progress_percentage
    st.progress(progress / 100, text=f"Overall Progress: {progress:.0f}%")
    
    # Current step highlight
    current_step = process.current_step
    if current_step:
        step_emoji = get_step_emoji(current_step.step_type)
        st.info(f"**Currently:** {step_emoji} {current_step.description}")
    
    # All steps
    for i, step in enumerate(process.steps):
        step_emoji = get_step_emoji(step.step_type)
        status_emoji = get_status_emoji(step.status)
        
        # Create expandable section for each step
        is_current = step.is_active
        is_completed = step.status == ThinkingStepStatus.COMPLETED
        
        if is_current:
            st.markdown(f"**{status_emoji} {step_emoji} {step.description}** *(in progress)*")
        elif is_completed:
            duration_str = format_duration(step.duration)
            st.markdown(f"{status_emoji} {step_emoji} {step.description} *({duration_str})*")
        else:
            st.markdown(f"{status_emoji} {step_emoji} {step.description}")


def create_live_thinking_display(process_id: str) -> Dict[str, Any]:
    """
    Create placeholders for live thinking display that can be updated
    
    Returns:
        Dictionary of placeholders for updating the display
    """
    # Main thinking container
    thinking_container = st.container()
    
    with thinking_container:
        st.markdown("### 🧠 Agent Thinking Process")
        
        # Progress section
        progress_placeholder = st.empty()
        
        # Current step section
        current_step_placeholder = st.empty()
        
        # Detailed steps (collapsible)
        with st.expander("📋 Detailed Steps", expanded=False):
            details_placeholder = st.empty()
        
        # Step details (collapsible)
        with st.expander("🔧 Step Details", expanded=False):
            step_details_placeholder = st.empty()
    
    return {
        "progress": progress_placeholder,
        "current_step": current_step_placeholder,
        "details": details_placeholder,
        "step_details": step_details_placeholder,
        "container": thinking_container
    }


def update_live_display(process_id: str, placeholders: Dict[str, Any]) -> None:
    """Update the live thinking display with current process state"""
    tracker = get_process_tracker()
    process = tracker.get_process(process_id)
    
    if not process:
        return
    
    # Update progress
    with placeholders["progress"]:
        progress = process.progress_percentage
        st.progress(progress / 100, text=f"Progress: {progress:.0f}%")
    
    # Update current step
    with placeholders["current_step"]:
        current_step = process.current_step
        if current_step:
            step_emoji = get_step_emoji(current_step.step_type)
            status_emoji = get_status_emoji(current_step.status)
            st.markdown(f"**{status_emoji} {step_emoji} {current_step.description}**")
            
            # Show duration if step is active
            if current_step.start_time:
                elapsed = (datetime.now() - current_step.start_time).total_seconds()
                st.caption(f"Running for {format_duration(elapsed)}")
        elif process.status == "completed":
            st.success("✅ All steps completed")
        elif process.status == "failed":
            st.error("❌ Process failed")
        else:
            st.info("🤔 Initializing...")
    
    # Update detailed steps
    with placeholders["details"]:
        for step in process.steps:
            step_emoji = get_step_emoji(step.step_type)
            status_emoji = get_status_emoji(step.status)
            
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                st.markdown(f"{status_emoji} {step_emoji} {step.description}")
            
            with col2:
                if step.status == ThinkingStepStatus.IN_PROGRESS:
                    st.markdown("*Running...*")
                elif step.status == ThinkingStepStatus.COMPLETED:
                    st.markdown("*Done*")
                elif step.status == ThinkingStepStatus.FAILED:
                    st.markdown("*Failed*")
                else:
                    st.markdown("*Pending*")
            
            with col3:
                duration_str = format_duration(step.duration)
                st.markdown(f"*{duration_str}*")
    
    # Update step details
    with placeholders["step_details"]:
        current_step = process.current_step
        if current_step and current_step.details:
            st.json(current_step.details)
        else:
            st.markdown("*No details available*")


def create_collapsible_thinking_display(session_id: str, expanded: bool = False) -> Optional[str]:
    """
    Create a collapsible thinking display for a session
    
    Returns:
        Process ID if a process is active, None otherwise
    """
    tracker = get_process_tracker()
    process = tracker.get_process_by_session(session_id)
    
    if not process:
        return None
    
    with st.expander("🧠 Agent Thinking Process", expanded=expanded):
        show_thinking_status(process.process_id, style="detailed")
    
    return process.process_id
